{"name": "@serenity-ai/api", "version": "1.0.0", "description": "Serenity AI Backend API", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "migrate": "node scripts/migrate.js", "setup": "node scripts/setup.js", "setup-uploads": "node scripts/setup-uploads.js", "seed": "node scripts/seed.js", "test": "jest"}, "dependencies": {"@google/generative-ai": "^0.24.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.0", "pg": "^8.11.3", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["serenity-ai", "mental-health", "api", "backend"], "author": "Serenity AI Team", "license": "MIT"}
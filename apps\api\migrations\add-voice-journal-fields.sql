-- Migration: Add voice journaling fields to journal_entries table
-- Date: 2024-12-19
-- Description: Adds support for voice journal entries with audio file storage

-- Add voice entry fields to journal_entries table
ALTER TABLE journal_entries 
ADD COLUMN IF NOT EXISTS is_voice_entry BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS audio_file_path VARCHAR(500),
ADD COLUMN IF NOT EXISTS voice_duration_seconds INTEGER DEFAULT 0;

-- Update journal_activities constraint to include voice_transcribed action
ALTER TABLE journal_activities 
DROP CONSTRAINT IF EXISTS valid_action;

ALTER TABLE journal_activities 
ADD CONSTRAINT valid_action CHECK (action IN (
    'create', 'update', 'delete', 'view', 'ai_summary_generated',
    'ai_insights_generated', 'ai_conversation_started', 'exported',
    'shared', 'tagged', 'mood_updated', 'voice_transcribed', 'voice_entry_created'
));

-- Create index for voice entries
CREATE INDEX IF NOT EXISTS idx_journal_entries_voice 
ON journal_entries(is_voice_entry) 
WHERE is_voice_entry = TRUE;

-- Create index for audio file paths
CREATE INDEX IF NOT EXISTS idx_journal_entries_audio_path 
ON journal_entries(audio_file_path) 
WHERE audio_file_path IS NOT NULL;

-- Update existing entries to set is_voice_entry = FALSE if NULL
UPDATE journal_entries 
SET is_voice_entry = FALSE 
WHERE is_voice_entry IS NULL;

COMMIT;

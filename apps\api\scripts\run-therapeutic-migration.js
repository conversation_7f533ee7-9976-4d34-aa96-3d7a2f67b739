import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { query, closePool } from '../config/database.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

async function runTherapeuticMigration() {
  try {
    console.log('🧠 Starting therapeutic sessions migration...')

    // Read the migration SQL file
    const sqlFilePath = path.join(__dirname, '..', 'migrations', '002_add_therapeutic_sessions.sql')
    const sql = fs.readFileSync(sqlFilePath, 'utf8')

    // Execute the migration SQL
    console.log('📊 Executing therapeutic sessions migration...')
    await query(sql)

    console.log('✅ Therapeutic sessions migration completed successfully!')
    console.log(`
🧠 Therapeutic Journaling Features Added:
   ✓ therapeutic_sessions table created
   ✓ ai_interactions constraints updated
   ✓ Indexes created for optimal performance
   ✓ Triggers added for timestamp management

🎯 Ready for advanced therapeutic journaling!
    `)

  } catch (error) {
    console.error('❌ Error running therapeutic migration:', error)
    process.exit(1)
  } finally {
    await closePool()
  }
}

// Run the migration
runTherapeuticMigration()

# Voice Transcription Setup Guide

This guide explains how to set up real voice transcription for the Serenity AI voice journaling feature.

## Overview

The voice journaling system supports two transcription services:
1. **OpenAI Whisper API** (Recommended - best accuracy)
2. **Google Speech-to-Text API** (Alternative option)

You can configure one or both services. The system will try OpenAI first, then fall back to Google if available.

## Option 1: OpenAI Whisper API (Recommended)

### Step 1: Get OpenAI API Key
1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the API key

### Step 2: Configure Environment
Add to your `.env` file:
```env
OPENAI_API_KEY=your_actual_openai_api_key_here
```

### Step 3: Install Dependencies
```bash
cd apps/api
npm install
```

### Benefits:
- Excellent accuracy for multiple languages
- Fast processing
- No additional setup required
- Supports various audio formats

## Option 2: Google Speech-to-Text API

### Step 1: Set up Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Speech-to-Text API
4. Create a service account
5. Download the service account JSON key file

### Step 2: Configure Environment
Add to your `.env` file:
```env
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json
GOOGLE_PROJECT_ID=your_google_project_id
```

### Step 3: Install Dependencies
```bash
cd apps/api
npm install @google-cloud/speech
```

### Benefits:
- High accuracy
- Real-time streaming support
- Advanced features like speaker diarization
- Free tier available

## Testing the Setup

### 1. Start the Server
```bash
cd apps/api
npm run dev
```

### 2. Test Voice Recording
1. Open the Serenity AI web app
2. Navigate to the Voice Journal widget
3. Click the microphone button to start recording
4. Speak for a few seconds
5. Click stop to end recording
6. The transcription should appear automatically

### 3. Check Logs
Monitor the server logs for transcription status:
- ✅ Success: "Transcription completed"
- ⚠️ Warning: Service fallback messages
- ❌ Error: Configuration issues

## Troubleshooting

### No Transcription Service Configured
**Error**: "Transcription service unavailable"
**Solution**: Add at least one API key to your `.env` file

### OpenAI API Errors
**Error**: "Whisper API error: 401"
**Solution**: Check your OpenAI API key is valid and has credits

### Google Cloud Errors
**Error**: "Google Speech transcription failed"
**Solutions**:
- Verify service account JSON path is correct
- Ensure Speech-to-Text API is enabled
- Check project ID is correct

### Audio Format Issues
**Error**: "Unsupported audio format"
**Solutions**:
- The system supports WebM, WAV, and MP4 formats
- Browser compatibility may vary
- Try using a different browser

## Manual Transcription Fallback

If automatic transcription fails, users can:
1. Record their voice normally
2. Click "Add Text" to manually type the transcription
3. Save the entry with both audio and text

This ensures voice journaling works even without transcription services configured.

## Cost Considerations

### OpenAI Whisper Pricing
- $0.006 per minute of audio
- Very cost-effective for most use cases
- No setup fees

### Google Speech-to-Text Pricing
- First 60 minutes free per month
- $0.024 per minute after free tier
- Additional features may have extra costs

## Security Notes

- API keys should never be committed to version control
- Use environment variables for all sensitive configuration
- Consider rotating API keys regularly
- Monitor API usage to prevent unexpected charges

## Support

For additional help:
1. Check the server logs for detailed error messages
2. Verify your API keys are valid and have sufficient credits
3. Test with a simple audio file first
4. Contact support if issues persist

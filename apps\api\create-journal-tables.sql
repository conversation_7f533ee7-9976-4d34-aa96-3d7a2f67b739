-- ===================================================================
-- 📖 SERENITY AI JOURNAL DATABASE SCHEMA
-- ===================================================================
-- Advanced journaling system with AI integration and human-AI interaction storage
-- Comprehensive tracking of journal entries, AI analysis, and user activities
-- ===================================================================

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ===================================================================
-- 📝 JOURNAL ENTRIES TABLE
-- ===================================================================
-- Main table for storing user journal entries with AI analysis capabilities

CREATE TABLE IF NOT EXISTS journal_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200),
    content TEXT NOT NULL,
    mood VARCHAR(50),
    emotional_tone VARCHAR(50),
    tags JSONB DEFAULT '[]'::jsonb,
    word_count INTEGER DEFAULT 0,

    -- AI Analysis Fields
    ai_summary TEXT,
    ai_insights JSONB,
    has_ai_summary BOOLEAN DEFAULT FALSE,
    has_ai_insights BOOLEAN DEFAULT FALSE,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Indexes for performance
    CONSTRAINT valid_mood CHECK (mood IN (
        'happy', 'calm', 'neutral', 'sad', 'anxious',
        'frustrated', 'thoughtful', 'motivated', 'excited',
        'worried', 'content', 'overwhelmed', 'grateful', 'hopeful'
    )),
    CONSTRAINT valid_emotional_tone CHECK (emotional_tone IN (
        'positive', 'negative', 'neutral', 'anxious', 'sad', 'happy',
        'reflective', 'stressed', 'grateful', 'hopeful', 'frustrated',
        'calm', 'excited', 'worried', 'content', 'overwhelmed'
    )),
    CONSTRAINT valid_word_count CHECK (word_count >= 0)
);

-- Create indexes for journal_entries
CREATE INDEX IF NOT EXISTS idx_journal_entries_user_id ON journal_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_journal_entries_created_at ON journal_entries(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_journal_entries_mood ON journal_entries(mood);
CREATE INDEX IF NOT EXISTS idx_journal_entries_emotional_tone ON journal_entries(emotional_tone);
CREATE INDEX IF NOT EXISTS idx_journal_entries_has_ai_summary ON journal_entries(has_ai_summary);
CREATE INDEX IF NOT EXISTS idx_journal_entries_has_ai_insights ON journal_entries(has_ai_insights);
CREATE INDEX IF NOT EXISTS idx_journal_entries_tags ON journal_entries USING GIN(tags);

-- Full-text search index for content and title
CREATE INDEX IF NOT EXISTS idx_journal_entries_search ON journal_entries
USING GIN(to_tsvector('english', title || ' ' || content));

-- ===================================================================
-- 🤖 AI INTERACTIONS TABLE
-- ===================================================================
-- Comprehensive logging of all AI interactions for journal analysis

CREATE TABLE IF NOT EXISTS ai_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    journal_entry_id UUID REFERENCES journal_entries(id) ON DELETE CASCADE,
    interaction_type VARCHAR(50) NOT NULL,
    prompt TEXT NOT NULL,
    response TEXT NOT NULL,

    -- Metadata
    model_used VARCHAR(100) DEFAULT 'gemini-pro',
    processing_time_ms INTEGER,
    token_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT valid_interaction_type CHECK (interaction_type IN (
        'summary', 'insights', 'emotional_patterns', 'cognitive_analysis',
        'therapeutic_suggestions', 'reflection_questions', 'mood_analysis',
        'pattern_detection', 'wellness_check', 'encouragement', 'therapeutic_processing',
        'closing_reflection'
    ))
);

-- ===================================================================
-- 🧠 THERAPEUTIC JOURNALING SESSIONS TABLE
-- ===================================================================

CREATE TABLE IF NOT EXISTS therapeutic_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Session data
    emotional_state VARCHAR(50) NOT NULL,
    session_data JSONB NOT NULL,

    -- Session metadata
    session_type VARCHAR(50) DEFAULT 'guided_journaling',
    status VARCHAR(20) DEFAULT 'active',

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT valid_session_status CHECK (status IN ('active', 'completed', 'abandoned')),
    CONSTRAINT valid_emotional_state CHECK (emotional_state IN (
        'happy', 'sad', 'anxious', 'angry', 'frustrated', 'hopeful', 'confused',
        'overwhelmed', 'calm', 'excited', 'disappointed', 'grateful', 'lonely',
        'content', 'worried', 'peaceful', 'neutral', 'thoughtful', 'motivated'
    ))
);

-- Create indexes for ai_interactions
CREATE INDEX IF NOT EXISTS idx_ai_interactions_user_id ON ai_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_interactions_journal_entry_id ON ai_interactions(journal_entry_id);
CREATE INDEX IF NOT EXISTS idx_ai_interactions_type ON ai_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_ai_interactions_created_at ON ai_interactions(created_at DESC);

-- Create indexes for therapeutic_sessions
CREATE INDEX IF NOT EXISTS idx_therapeutic_sessions_user_id ON therapeutic_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_therapeutic_sessions_status ON therapeutic_sessions(status);
CREATE INDEX IF NOT EXISTS idx_therapeutic_sessions_emotional_state ON therapeutic_sessions(emotional_state);
CREATE INDEX IF NOT EXISTS idx_therapeutic_sessions_created_at ON therapeutic_sessions(created_at);

-- ===================================================================
-- 💬 AI CONVERSATIONS TABLE
-- ===================================================================
-- Storage for ongoing AI conversations related to journal entries

CREATE TABLE IF NOT EXISTS ai_conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    journal_entry_id UUID REFERENCES journal_entries(id) ON DELETE CASCADE,
    conversation_type VARCHAR(50) NOT NULL,
    title VARCHAR(200),

    -- Conversation State
    is_active BOOLEAN DEFAULT TRUE,
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    message_count INTEGER DEFAULT 0,

    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT valid_conversation_type CHECK (conversation_type IN (
        'reflection', 'coaching', 'mindfulness', 'storytelling',
        'journaling_companion', 'crisis_support', 'goal_setting',
        'emotional_processing', 'cognitive_reframing'
    ))
);

-- Create indexes for ai_conversations
CREATE INDEX IF NOT EXISTS idx_ai_conversations_user_id ON ai_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_conversations_journal_entry_id ON ai_conversations(journal_entry_id);
CREATE INDEX IF NOT EXISTS idx_ai_conversations_type ON ai_conversations(conversation_type);
CREATE INDEX IF NOT EXISTS idx_ai_conversations_active ON ai_conversations(is_active);
CREATE INDEX IF NOT EXISTS idx_ai_conversations_last_message ON ai_conversations(last_message_at DESC);

-- ===================================================================
-- 💬 AI CONVERSATION MESSAGES TABLE
-- ===================================================================
-- Individual messages within AI conversations

CREATE TABLE IF NOT EXISTS ai_conversation_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL REFERENCES ai_conversations(id) ON DELETE CASCADE,
    sender_type VARCHAR(20) NOT NULL,
    content TEXT NOT NULL,

    -- Message Metadata
    message_type VARCHAR(50) DEFAULT 'text',
    emotional_tone VARCHAR(50),
    is_voice_message BOOLEAN DEFAULT FALSE,
    voice_duration_seconds INTEGER,

    -- AI Response Metadata (for AI messages)
    prompt_used TEXT,
    model_used VARCHAR(100),
    processing_time_ms INTEGER,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT valid_sender_type CHECK (sender_type IN ('user', 'ai')),
    CONSTRAINT valid_message_type CHECK (message_type IN (
        'text', 'voice', 'image', 'reflection_prompt', 'insight',
        'encouragement', 'question', 'summary', 'suggestion'
    ))
);

-- Create indexes for ai_conversation_messages
CREATE INDEX IF NOT EXISTS idx_ai_conversation_messages_conversation_id ON ai_conversation_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_ai_conversation_messages_sender_type ON ai_conversation_messages(sender_type);
CREATE INDEX IF NOT EXISTS idx_ai_conversation_messages_created_at ON ai_conversation_messages(created_at DESC);

-- ===================================================================
-- 📊 JOURNAL ACTIVITIES TABLE
-- ===================================================================
-- Comprehensive activity logging for journal-related actions

CREATE TABLE IF NOT EXISTS journal_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    journal_entry_id UUID REFERENCES journal_entries(id) ON DELETE SET NULL,
    action VARCHAR(50) NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT valid_action CHECK (action IN (
        'create', 'update', 'delete', 'view', 'ai_summary_generated',
        'ai_insights_generated', 'ai_conversation_started', 'exported',
        'shared', 'tagged', 'mood_updated', 'voice_transcribed'
    ))
);

-- Create indexes for journal_activities
CREATE INDEX IF NOT EXISTS idx_journal_activities_user_id ON journal_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_journal_activities_journal_entry_id ON journal_activities(journal_entry_id);
CREATE INDEX IF NOT EXISTS idx_journal_activities_action ON journal_activities(action);
CREATE INDEX IF NOT EXISTS idx_journal_activities_created_at ON journal_activities(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_journal_activities_metadata ON journal_activities USING GIN(metadata);

-- ===================================================================
-- 🎯 JOURNAL PROMPTS TABLE
-- ===================================================================
-- AI-generated and curated writing prompts for users

CREATE TABLE IF NOT EXISTS journal_prompts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    category VARCHAR(50) NOT NULL,
    difficulty_level VARCHAR(20) DEFAULT 'medium',

    -- Targeting
    target_mood VARCHAR(50),
    target_emotional_tone VARCHAR(50),
    is_ai_generated BOOLEAN DEFAULT FALSE,

    -- Usage Statistics
    usage_count INTEGER DEFAULT 0,
    average_rating DECIMAL(3,2) DEFAULT 0.00,

    -- Metadata
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT valid_category CHECK (category IN (
        'reflection', 'gratitude', 'goals', 'relationships', 'mindfulness',
        'creativity', 'problem_solving', 'emotional_processing', 'self_discovery',
        'daily_check_in', 'therapeutic', 'cbt_exercises'
    )),
    CONSTRAINT valid_difficulty CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
    CONSTRAINT valid_rating CHECK (average_rating >= 0.00 AND average_rating <= 5.00)
);

-- Create indexes for journal_prompts
CREATE INDEX IF NOT EXISTS idx_journal_prompts_category ON journal_prompts(category);
CREATE INDEX IF NOT EXISTS idx_journal_prompts_difficulty ON journal_prompts(difficulty_level);
CREATE INDEX IF NOT EXISTS idx_journal_prompts_target_mood ON journal_prompts(target_mood);
CREATE INDEX IF NOT EXISTS idx_journal_prompts_active ON journal_prompts(is_active);
CREATE INDEX IF NOT EXISTS idx_journal_prompts_usage ON journal_prompts(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_journal_prompts_rating ON journal_prompts(average_rating DESC);

-- ===================================================================
-- 🔄 UPDATE TRIGGERS
-- ===================================================================

-- Update timestamp trigger for journal_entries
CREATE OR REPLACE FUNCTION update_journal_entries_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_journal_entries_updated_at
    BEFORE UPDATE ON journal_entries
    FOR EACH ROW
    EXECUTE FUNCTION update_journal_entries_updated_at();

-- Update timestamp trigger for ai_conversations
CREATE OR REPLACE FUNCTION update_ai_conversations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_ai_conversations_updated_at
    BEFORE UPDATE ON ai_conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_ai_conversations_updated_at();

-- Update message count trigger for ai_conversations
CREATE OR REPLACE FUNCTION update_conversation_message_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE ai_conversations
        SET message_count = message_count + 1,
            last_message_at = NEW.created_at
        WHERE id = NEW.conversation_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE ai_conversations
        SET message_count = message_count - 1
        WHERE id = OLD.conversation_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_conversation_message_count
    AFTER INSERT OR DELETE ON ai_conversation_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_conversation_message_count();

-- ===================================================================
-- 📊 SAMPLE DATA (Optional - for development/testing)
-- ===================================================================

-- Insert sample journal prompts
INSERT INTO journal_prompts (title, content, category, difficulty_level, target_mood, is_ai_generated) VALUES
('Daily Reflection', 'What are three things that happened today that you''re grateful for? How did they make you feel?', 'reflection', 'easy', 'neutral', false),
('Emotional Check-in', 'Take a moment to identify what you''re feeling right now. Where do you feel this emotion in your body?', 'emotional_processing', 'medium', 'anxious', false),
('Goal Setting', 'What is one small step you can take tomorrow toward a goal that matters to you?', 'goals', 'medium', 'motivated', false),
('Mindful Moment', 'Describe your current environment using all five senses. What do you see, hear, smell, taste, and feel?', 'mindfulness', 'easy', 'calm', false),
('Relationship Reflection', 'Think about a meaningful relationship in your life. What do you appreciate most about this person?', 'relationships', 'medium', 'grateful', false)
ON CONFLICT DO NOTHING;

-- ===================================================================
-- 🎉 SCHEMA CREATION COMPLETE
-- ===================================================================
-- Journal database schema has been successfully created with:
-- - 6 main tables for comprehensive journal functionality
-- - Advanced indexing for optimal performance
-- - Triggers for automatic timestamp and count updates
-- - Sample data for immediate testing
-- - Full support for AI integration and human-AI interaction storage
-- ===================================================================

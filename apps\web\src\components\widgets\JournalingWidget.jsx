import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { motion, AnimatePresence } from 'framer-motion'
import {
  BookOpen, Plus, Eye, Sparkles, Brain, Heart, MessageCircle,
  Mic, MicOff, Save, X, Edit3, Trash2, Download, Search,
  Calendar, TrendingUp, Zap, Bot, PenTool, FileText, AlertTriangle
} from 'lucide-react'
import Card from '../shared/Card'
import { journalAPI, chatAPI } from '../../services/api'
import AIWellnessCompanion from '../wellness/AIWellnessCompanion'
import useBodyScrollLock from '../../hooks/useBodyScrollLock'
import useModalKeyboard from '../../hooks/useModalKeyboard'

const JournalingWidget = () => {
  // State management
  const [journalEntries, setJournalEntries] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  // Modal states
  const [showNewEntryModal, setShowNewEntryModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [showAIInsightsModal, setShowAIInsightsModal] = useState(false)
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false)
  const [selectedEntry, setSelectedEntry] = useState(null)
  const [entryToDelete, setEntryToDelete] = useState(null)

  // Entry creation/editing states
  const [entryContent, setEntryContent] = useState('')
  const [entryTitle, setEntryTitle] = useState('')
  const [entryMood, setEntryMood] = useState('')
  const [entryTags, setEntryTags] = useState([])
  const [isEditing, setIsEditing] = useState(false)
  const [editingEntryId, setEditingEntryId] = useState(null)

  // AI features states
  const [aiInsights, setAiInsights] = useState(null)
  const [aiSummary, setAiSummary] = useState(null)
  const [isGeneratingAI, setIsGeneratingAI] = useState(false)
  const [aiConversation, setAiConversation] = useState([])
  const [aiMessage, setAiMessage] = useState('')

  // Voice recording states
  const [isRecording, setIsRecording] = useState(false)
  const [mediaRecorder, setMediaRecorder] = useState(null)
  const [audioChunks, setAudioChunks] = useState([])

  // AI Companion states
  const [showAICompanion, setShowAICompanion] = useState(false)
  const [aiCompanionMessage, setAiCompanionMessage] = useState('')
  const [aiInteractionType, setAiInteractionType] = useState('encouragement')
  const [requiresUserResponse, setRequiresUserResponse] = useState(false)

  // Therapeutic Journaling states
  const [showTherapeuticModal, setShowTherapeuticModal] = useState(false)
  const [therapeuticSession, setTherapeuticSession] = useState(null)
  const [selectedJournalingPath, setSelectedJournalingPath] = useState(null)
  const [therapeuticPrompts, setTherapeuticPrompts] = useState(null)
  const [currentPromptIndex, setCurrentPromptIndex] = useState(0)
  const [therapeuticResponses, setTherapeuticResponses] = useState([])
  const [currentResponse, setCurrentResponse] = useState('')
  const [sessionProgress, setSessionProgress] = useState('check-in') // check-in, path-selection, journaling, reflection
  const [emotionalCheckIn, setEmotionalCheckIn] = useState('')
  const [showClosingReflection, setShowClosingReflection] = useState(false)
  const [closingReflection, setClosingReflection] = useState(null)

  // Search and filter states
  const [searchQuery, setSearchQuery] = useState('')
  const [moodFilter, setMoodFilter] = useState('')
  const [dateFilter, setDateFilter] = useState('')

  // Delete confirmation functions
  const cancelDeleteEntry = () => {
    setShowDeleteConfirmModal(false)
    setEntryToDelete(null)
  }

  // Lock body scroll when modals are open
  useBodyScrollLock(showNewEntryModal || showViewModal || showAIInsightsModal || showDeleteConfirmModal || showTherapeuticModal)

  // Handle keyboard events for modals
  useModalKeyboard(showNewEntryModal, () => setShowNewEntryModal(false))
  useModalKeyboard(showViewModal, () => setShowViewModal(false))
  useModalKeyboard(showAIInsightsModal, () => setShowAIInsightsModal(false))
  useModalKeyboard(showDeleteConfirmModal, cancelDeleteEntry)
  useModalKeyboard(showTherapeuticModal, () => {
    resetTherapeuticSession()
    setShowTherapeuticModal(false)
  })

  // Mood options for selection
  const moodOptions = [
    { emoji: '😊', label: 'Happy', value: 'happy' },
    { emoji: '😌', label: 'Calm', value: 'calm' },
    { emoji: '😐', label: 'Neutral', value: 'neutral' },
    { emoji: '😔', label: 'Sad', value: 'sad' },
    { emoji: '😰', label: 'Anxious', value: 'anxious' },
    { emoji: '😤', label: 'Frustrated', value: 'frustrated' },
    { emoji: '🤔', label: 'Thoughtful', value: 'thoughtful' },
    { emoji: '💪', label: 'Motivated', value: 'motivated' }
  ]

  // Load journal entries on component mount
  useEffect(() => {
    loadJournalEntries()
  }, [moodFilter, dateFilter])

  // API Functions
  const loadJournalEntries = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const response = await journalAPI.getEntries(20, 0, moodFilter, dateFilter)
      setJournalEntries(response.data || [])
    } catch (error) {
      console.error('Failed to load journal entries:', error)
      setError('Failed to load journal entries')
      // Fallback to mock data for demo
      setJournalEntries([
        {
          id: 1,
          title: 'Mindfulness Reflection',
          content: 'Had an interesting conversation with my therapist today about mindfulness. I\'m starting to understand how my thoughts affect my emotions and how I can observe them without judgment...',
          mood: 'thoughtful',
          wordCount: 245,
          createdAt: new Date().toISOString(),
          hasAISummary: true,
          hasAIInsights: true,
          emotionalTone: 'reflective'
        },
        {
          id: 2,
          title: 'Gratitude Practice',
          content: 'Feeling grateful for the small moments today. The morning coffee, the sunset, and the call with mom made me realize how much beauty surrounds us when we pay attention...',
          mood: 'happy',
          wordCount: 189,
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          hasAISummary: true,
          hasAIInsights: false,
          emotionalTone: 'positive'
        },
        {
          id: 3,
          title: 'Work Stress',
          content: 'Work stress is getting to me again. I need to remember the breathing techniques we discussed and maybe try some of the cognitive reframing exercises...',
          mood: 'anxious',
          wordCount: 156,
          createdAt: new Date(Date.now() - 172800000).toISOString(),
          hasAISummary: false,
          hasAIInsights: false,
          emotionalTone: 'stressed'
        }
      ])
    } finally {
      setIsLoading(false)
    }
  }

  const saveJournalEntry = async () => {
    try {
      setIsLoading(true)
      const entryData = {
        content: entryContent,
        mood: entryMood,
        title: entryTitle || generateTitleFromContent(entryContent),
        tags: entryTags
      }

      let response
      if (isEditing && editingEntryId) {
        response = await journalAPI.updateEntry(editingEntryId, entryData.content, entryData.mood, entryData.title, entryData.tags)
      } else {
        response = await journalAPI.createEntry(entryData.content, entryData.mood, entryData.title, entryData.tags)
      }

      if (response.success) {
        await loadJournalEntries()
        resetEntryForm()
        setShowNewEntryModal(false)

        // Trigger AI companion encouragement
        triggerAIInteraction('encouragement',
          `Beautiful entry! I can sense the depth in your reflection. Writing helps us process our thoughts and emotions. How do you feel after expressing yourself? ✨`,
          true
        )
      }
    } catch (error) {
      console.error('Failed to save journal entry:', error)
      setError('Failed to save journal entry')
    } finally {
      setIsLoading(false)
    }
  }

  const generateTitleFromContent = (content) => {
    if (!content) return 'Untitled Entry'
    const words = content.split(' ').slice(0, 5).join(' ')
    return words.length > 30 ? words.substring(0, 30) + '...' : words
  }

  const resetEntryForm = () => {
    setEntryContent('')
    setEntryTitle('')
    setEntryMood('')
    setEntryTags([])
    setIsEditing(false)
    setEditingEntryId(null)
  }

  // ===================================================================
  // 🧠 THERAPEUTIC JOURNALING FUNCTIONS
  // ===================================================================

  // Start therapeutic journaling session
  const handleStartTherapeuticJournal = async () => {
    try {
      setIsLoading(true)
      setError(null) // Clear any previous errors

      console.log('🧠 Starting therapeutic journaling session...')

      // Start therapeutic session with emotional check-in
      const response = await journalAPI.startTherapeuticSession('neutral', {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
      })

      console.log('🧠 Therapeutic session response:', response)

      if (response && response.success) {
        console.log('✅ Therapeutic session started successfully')
        setTherapeuticSession(response.data)
        setSessionProgress('check-in')
        setShowTherapeuticModal(true)
      } else {
        console.error('❌ Therapeutic session failed:', response)
        setError(response?.message || 'Failed to start therapeutic journaling session')
      }
    } catch (error) {
      console.error('❌ Failed to start therapeutic session:', error)

      // More detailed error handling
      if (error.response) {
        // Server responded with error status
        const errorMessage = error.response.data?.message || 'Server error occurred'
        setError(`Server Error: ${errorMessage}`)
        console.error('Server error details:', error.response.data)
      } else if (error.request) {
        // Request was made but no response received
        setError('Network error: Unable to connect to server')
        console.error('Network error:', error.request)
      } else {
        // Something else happened
        setError(`Error: ${error.message}`)
        console.error('General error:', error.message)
      }
    } finally {
      setIsLoading(false)
    }
  }

  // Handle emotional check-in response
  const handleEmotionalCheckIn = async (emotionalState) => {
    try {
      setEmotionalCheckIn(emotionalState)
      setIsLoading(true)

      // Get adaptive journaling paths based on emotional state
      const response = await journalAPI.getAdaptiveJournalingPaths(emotionalState, journalEntries.slice(0, 3))

      if (response.success) {
        setTherapeuticSession(prev => ({
          ...prev,
          adaptivePaths: response.data,
          emotionalState
        }))
        setSessionProgress('path-selection')
      }
    } catch (error) {
      console.error('Failed to get adaptive paths:', error)
      setError('Failed to get journaling paths')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle journaling path selection
  const handlePathSelection = async (pathId) => {
    try {
      setSelectedJournalingPath(pathId)
      setIsLoading(true)

      // Get therapeutic prompts for selected path
      const response = await journalAPI.getTherapeuticPrompts(pathId, emotionalCheckIn)

      if (response.success) {
        setTherapeuticPrompts(response.data)
        setSessionProgress('journaling')
        setCurrentPromptIndex(0)
      }
    } catch (error) {
      console.error('Failed to get therapeutic prompts:', error)
      setError('Failed to get journaling prompts')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle therapeutic response submission
  const handleTherapeuticResponse = async (response) => {
    try {
      setIsLoading(true)

      // Process response with AI guidance
      const processingResponse = await journalAPI.processTherapeuticResponse(
        response,
        therapeuticPrompts?.technique || 'reflection',
        {
          sessionId: therapeuticSession?.sessionId,
          pathType: selectedJournalingPath,
          emotionalState: emotionalCheckIn
        }
      )

      if (processingResponse.success) {
        // Add response to therapeutic responses
        setTherapeuticResponses(prev => [...prev, {
          prompt: therapeuticPrompts?.primaryPrompt,
          response: response,
          aiGuidance: processingResponse.data,
          timestamp: new Date().toISOString()
        }])

        // Add to entry content
        setEntryContent(prev => prev + (prev ? '\n\n' : '') + response)

        // Check if we should continue with follow-up questions or move to reflection
        if (therapeuticPrompts?.followUpQuestions && currentPromptIndex < therapeuticPrompts.followUpQuestions.length) {
          setCurrentPromptIndex(prev => prev + 1)
        } else {
          // Move to closing reflection
          await handleGenerateClosingReflection()
        }
      }
    } catch (error) {
      console.error('Failed to process therapeutic response:', error)
      setError('Failed to process your response')
    } finally {
      setIsLoading(false)
    }
  }

  // Generate closing reflection
  const handleGenerateClosingReflection = async () => {
    try {
      setIsLoading(true)

      const response = await journalAPI.generateClosingReflection(
        therapeuticSession,
        entryContent
      )

      if (response.success) {
        setClosingReflection(response.data)
        setSessionProgress('reflection')
        setShowClosingReflection(true)
      }
    } catch (error) {
      console.error('Failed to generate closing reflection:', error)
      setError('Failed to generate closing reflection')
    } finally {
      setIsLoading(false)
    }
  }

  // Complete therapeutic journaling session
  const handleCompleteTherapeuticSession = async () => {
    try {
      setIsLoading(true)

      // Save the journal entry with therapeutic context
      const entryData = {
        content: entryContent,
        mood: emotionalCheckIn,
        title: generateTitleFromContent(entryContent),
        tags: ['therapeutic-journaling', selectedJournalingPath],
        therapeuticData: {
          sessionId: therapeuticSession?.sessionId,
          pathType: selectedJournalingPath,
          responses: therapeuticResponses,
          closingReflection: closingReflection
        }
      }

      const response = await journalAPI.createEntry(
        entryData.content,
        entryData.mood,
        entryData.title,
        entryData.tags
      )

      if (response.success) {
        await loadJournalEntries()
        resetTherapeuticSession()
        setShowTherapeuticModal(false)

        // Trigger AI companion encouragement
        triggerAIInteraction('encouragement',
          `What a beautiful therapeutic journaling session! I can sense the depth and intention you brought to this reflection. Your willingness to explore and grow is truly inspiring. How are you feeling after this experience? ✨`,
          true
        )
      }
    } catch (error) {
      console.error('Failed to save therapeutic journal entry:', error)
      setError('Failed to save your therapeutic journal entry')
    } finally {
      setIsLoading(false)
    }
  }

  // Reset therapeutic session
  const resetTherapeuticSession = () => {
    setTherapeuticSession(null)
    setSelectedJournalingPath(null)
    setTherapeuticPrompts(null)
    setCurrentPromptIndex(0)
    setTherapeuticResponses([])
    setCurrentResponse('')
    setSessionProgress('check-in')
    setEmotionalCheckIn('')
    setShowClosingReflection(false)
    setClosingReflection(null)
    resetEntryForm()
  }

  // Event Handlers
  const handleNewEntry = () => {
    resetEntryForm()
    setShowNewEntryModal(true)
  }

  // Handle therapeutic journaling button
  const handleTherapeuticJournal = () => {
    console.log('🧠 Therapeutic Journal button clicked!')
    handleStartTherapeuticJournal()
  }



  const handleEditEntry = (entry) => {
    setEntryContent(entry.content)
    setEntryTitle(entry.title)
    setEntryMood(entry.mood)
    setEntryTags(entry.tags || [])
    setIsEditing(true)
    setEditingEntryId(entry.id)
    setShowNewEntryModal(true)
  }

  const handleViewEntry = (entry) => {
    setSelectedEntry(entry)
    setShowViewModal(true)
  }

  const handleDeleteEntry = (entryId) => {
    const entry = journalEntries.find(e => e.id === entryId)
    setEntryToDelete(entry)
    setShowDeleteConfirmModal(true)
  }

  const confirmDeleteEntry = async () => {
    if (!entryToDelete) return

    try {
      await journalAPI.deleteEntry(entryToDelete.id)
      await loadJournalEntries()
      setShowDeleteConfirmModal(false)
      setEntryToDelete(null)
    } catch (error) {
      console.error('Failed to delete entry:', error)
      setError('Failed to delete entry')
    }
  }



  const handleGenerateAISummary = async (entryId) => {
    try {
      setIsGeneratingAI(true)
      const response = await journalAPI.generateSummary(entryId, 'medium')
      setAiSummary(response.data)

      // Update the entry to mark it as having AI summary
      const updatedEntries = journalEntries.map(entry =>
        entry.id === entryId ? { ...entry, hasAISummary: true } : entry
      )
      setJournalEntries(updatedEntries)
    } catch (error) {
      console.error('Failed to generate AI summary:', error)
      setError('Failed to generate AI summary')
    } finally {
      setIsGeneratingAI(false)
    }
  }

  const handleGenerateAIInsights = async (entryId) => {
    try {
      setIsGeneratingAI(true)
      const response = await journalAPI.generateInsights(entryId, 'comprehensive')
      setAiInsights(response.data)
      setSelectedEntry(journalEntries.find(entry => entry.id === entryId))
      setShowAIInsightsModal(true)
    } catch (error) {
      console.error('Failed to generate AI insights:', error)
      setError('Failed to generate AI insights')
    } finally {
      setIsGeneratingAI(false)
    }
  }

  // AI Companion Functions
  const triggerAIInteraction = (type, message, requiresResponse = false) => {
    setAiInteractionType(type)
    setAiCompanionMessage(message)
    setRequiresUserResponse(requiresResponse)
    setShowAICompanion(true)
  }

  const handleAIResponse = (userResponse) => {
    console.log('User responded to AI:', userResponse)
    setShowAICompanion(false)
    setRequiresUserResponse(false)

    // Generate follow-up based on user response
    if (requiresUserResponse) {
      setTimeout(() => {
        if (userResponse.toLowerCase().includes('good') || userResponse.toLowerCase().includes('better') || userResponse.toLowerCase().includes('relieved')) {
          triggerAIInteraction('encouragement', "That's wonderful to hear! Journaling is such a powerful tool for emotional processing. Keep nurturing this practice. 🌱", false)
        } else if (userResponse.toLowerCase().includes('difficult') || userResponse.toLowerCase().includes('hard') || userResponse.toLowerCase().includes('overwhelming')) {
          triggerAIInteraction('support', "I understand. Sometimes putting our thoughts into words can feel challenging. Remember, there's no right or wrong way to journal. You're doing great. 💙", false)
        } else {
          triggerAIInteraction('encouragement', "Thank you for sharing your feelings with me. Your self-awareness is a strength. 🌟", false)
        }
      }, 2000)
    }
  }

  const handleAIDismiss = () => {
    setShowAICompanion(false)
    setRequiresUserResponse(false)
  }

  // Voice Recording Functions
  const startVoiceRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const recorder = new MediaRecorder(stream)

      recorder.ondataavailable = (event) => {
        setAudioChunks(prev => [...prev, event.data])
      }

      recorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/wav' })
        try {
          const response = await journalAPI.transcribeVoice(audioBlob)
          setEntryContent(prev => prev + ' ' + response.data.transcription)
        } catch (error) {
          console.error('Failed to transcribe voice:', error)
        }
        setAudioChunks([])
      }

      setMediaRecorder(recorder)
      recorder.start()
      setIsRecording(true)
    } catch (error) {
      console.error('Failed to start recording:', error)
      setError('Failed to access microphone')
    }
  }

  const stopVoiceRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop()
      mediaRecorder.stream.getTracks().forEach(track => track.stop())
      setIsRecording(false)
    }
  }

  // Utility Functions
  const formatDate = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now - date)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return 'Today'
    if (diffDays === 2) return 'Yesterday'
    if (diffDays <= 7) return `${diffDays - 1} days ago`
    return date.toLocaleDateString()
  }

  const getMoodEmoji = (mood) => {
    const moodOption = moodOptions.find(option => option.value === mood)
    return moodOption ? moodOption.emoji : '😐'
  }

  return (
    <>
      <Card className="journal-widget">
        <div className="journal-widget__header">
          <h3 className="widget__title">
            <BookOpen className="widget__icon" />
            Journal Entries
          </h3>
          <div className="journal-widget__actions">
            <button className="widget__action widget__action--therapeutic" onClick={handleTherapeuticJournal}>
              <Brain size={16} />
              Therapeutic Journal
            </button>
            <button className="widget__action" onClick={handleNewEntry}>
              <Plus size={16} />
              New Entry
            </button>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="journal-widget__loading">
            <motion.div
              className="journal-widget__loading-spinner"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <Sparkles size={24} />
            </motion.div>
            <p>Loading your journal entries...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <motion.div
            className="journal-widget__error"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <p>{error}</p>
            <button onClick={loadJournalEntries} className="journal-widget__retry-btn">
              Try Again
            </button>
          </motion.div>
        )}

        {/* Journal Entries */}
        {!isLoading && !error && (
          <>
            <div className="journal-widget__entries">
              {journalEntries.length === 0 ? (
                <motion.div
                  className="journal-widget__empty"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  <BookOpen size={48} className="journal-widget__empty-icon" />
                  <h4>Start Your Journaling Journey</h4>
                  <p>Your thoughts and reflections will appear here. Begin by writing your first entry.</p>
                </motion.div>
              ) : (
                journalEntries.map((entry, index) => (
                  <motion.div
                    key={entry.id}
                    className={`journal-widget__entry ${entry.hasAIInsights ? 'journal-widget__entry--ai-analyzed' : ''}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    onClick={() => handleViewEntry(entry)}
                  >
                    <div className="journal-widget__entry-header">
                      <div className="entry-meta">
                        <span className="journal-widget__entry-date">
                          {formatDate(entry.createdAt)}
                        </span>
                        <span className="journal-widget__entry-mood">
                          {getMoodEmoji(entry.mood)}
                        </span>
                      </div>
                      <div className="entry-stats">
                        <span>{entry.wordCount || entry.content?.split(' ').length || 0} words</span>
                        {entry.emotionalTone && (
                          <span className="emotional-tone">{entry.emotionalTone}</span>
                        )}
                      </div>
                    </div>

                    <h4 className="journal-widget__entry-title">{entry.title}</h4>

                    <p className="journal-widget__entry-preview">
                      {entry.content}
                    </p>

                    <div className="journal-widget__entry-actions" onClick={(e) => e.stopPropagation()}>
                      <button
                        className="journal-widget__entry-action"
                        onClick={() => handleViewEntry(entry)}
                      >
                        <Eye size={12} />
                        Read Full
                      </button>

                      <button
                        className="journal-widget__entry-action"
                        onClick={() => handleEditEntry(entry)}
                      >
                        <Edit3 size={12} />
                        Edit
                      </button>

                      {entry.hasAISummary ? (
                        <button
                          className="journal-widget__entry-action journal-widget__entry-action--ai"
                          onClick={() => handleGenerateAISummary(entry.id)}
                        >
                          <Sparkles size={12} />
                          AI Summary
                        </button>
                      ) : (
                        <button
                          className="journal-widget__entry-action journal-widget__entry-action--ai"
                          onClick={() => handleGenerateAISummary(entry.id)}
                          disabled={isGeneratingAI}
                        >
                          <Brain size={12} />
                          Generate Summary
                        </button>
                      )}

                      <button
                        className="journal-widget__entry-action journal-widget__entry-action--warm"
                        onClick={() => handleGenerateAIInsights(entry.id)}
                        disabled={isGeneratingAI}
                      >
                        <Zap size={12} />
                        AI Insights
                      </button>

                      <button
                        className="journal-widget__entry-action"
                        onClick={() => handleDeleteEntry(entry.id)}
                      >
                        <Trash2 size={12} />
                        Delete
                      </button>
                    </div>
                  </motion.div>
                ))
              )}
            </div>

            {/* New Entry Button */}
            <motion.button
              className="journal-widget__new-entry"
              onClick={handleNewEntry}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <PenTool size={20} />
              Start writing your thoughts...
            </motion.button>
          </>
        )}
      </Card>

      {/* New Entry Modal */}
      {showNewEntryModal && createPortal(
        <AnimatePresence>
          <motion.div
            className="journal-modal__overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowNewEntryModal(false)
              }
            }}
          >
            <motion.div
              className="journal-modal__content"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="journal-modal__header">
                <h2>{isEditing ? 'Edit Journal Entry' : 'New Journal Entry'}</h2>
                <button
                  onClick={() => setShowNewEntryModal(false)}
                  className="journal-modal__close"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="journal-modal__body">
                {/* Title Input */}
                <div className="journal-form__field">
                  <label htmlFor="entry-title">Title (optional)</label>
                  <input
                    id="entry-title"
                    type="text"
                    value={entryTitle}
                    onChange={(e) => setEntryTitle(e.target.value)}
                    placeholder="Give your entry a title..."
                    className="journal-form__input"
                  />
                </div>

                {/* Mood Selection */}
                <div className="journal-form__field">
                  <label>How are you feeling?</label>
                  <div className="journal-form__mood-grid">
                    {moodOptions.map((mood) => (
                      <button
                        key={mood.value}
                        type="button"
                        className={`journal-form__mood-option ${entryMood === mood.value ? 'journal-form__mood-option--selected' : ''}`}
                        onClick={() => setEntryMood(mood.value)}
                      >
                        <span className="mood-emoji">{mood.emoji}</span>
                        <span className="mood-label">{mood.label}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Content Textarea */}
                <div className="journal-form__field">
                  <label htmlFor="entry-content">Your thoughts and reflections</label>
                  <div className="journal-form__content-wrapper">
                    <textarea
                      id="entry-content"
                      value={entryContent}
                      onChange={(e) => setEntryContent(e.target.value)}
                      placeholder="Start writing your thoughts... What's on your mind today? How are you feeling? What insights have you discovered?"
                      className="journal-form__textarea"
                      rows={12}
                    />

                    {/* Voice Recording Button */}
                    <button
                      type="button"
                      className={`journal-form__voice-btn ${isRecording ? 'journal-form__voice-btn--recording' : ''}`}
                      onClick={isRecording ? stopVoiceRecording : startVoiceRecording}
                    >
                      {isRecording ? <MicOff size={16} /> : <Mic size={16} />}
                      {isRecording ? 'Stop Recording' : 'Voice to Text'}
                    </button>
                  </div>

                  <div className="journal-form__stats">
                    <span>{entryContent.split(' ').filter(word => word.length > 0).length} words</span>
                    <span>{entryContent.length} characters</span>
                  </div>
                </div>

                {/* Tags Input */}
                <div className="journal-form__field">
                  <label htmlFor="entry-tags">Tags (optional)</label>
                  <input
                    id="entry-tags"
                    type="text"
                    value={entryTags.join(', ')}
                    onChange={(e) => setEntryTags(e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag))}
                    placeholder="therapy, mindfulness, gratitude, work, relationships..."
                    className="journal-form__input"
                  />
                </div>
              </div>

              <div className="journal-modal__footer">
                <button
                  type="button"
                  onClick={() => setShowNewEntryModal(false)}
                  className="journal-btn journal-btn--secondary"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={saveJournalEntry}
                  disabled={!entryContent.trim() || isLoading}
                  className="journal-btn journal-btn--primary"
                >
                  <Save size={16} />
                  {isEditing ? 'Update Entry' : 'Save Entry'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}

      {/* View Entry Modal */}
      {showViewModal && selectedEntry && createPortal(
        <AnimatePresence>
          <motion.div
            className="journal-modal__overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowViewModal(false)
              }
            }}
          >
            <motion.div
              className="journal-modal__content journal-modal__content--large"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="journal-modal__header">
                <div className="journal-view__meta">
                  <h2>{selectedEntry.title}</h2>
                  <div className="journal-view__details">
                    <span className="journal-view__date">{formatDate(selectedEntry.createdAt)}</span>
                    <span className="journal-view__mood">{getMoodEmoji(selectedEntry.mood)}</span>
                    <span className="journal-view__stats">{selectedEntry.wordCount || selectedEntry.content?.split(' ').length || 0} words</span>
                  </div>
                </div>
                <button
                  onClick={() => setShowViewModal(false)}
                  className="journal-modal__close"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="journal-modal__body">
                <div className="journal-view__content">
                  {selectedEntry.content}
                </div>

                {/* AI Summary Section */}
                {aiSummary && (
                  <motion.div
                    className="journal-view__ai-section"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <h3>
                      <Sparkles size={20} />
                      AI Summary
                    </h3>
                    <div className="journal-view__ai-content">
                      {aiSummary}
                    </div>
                  </motion.div>
                )}
              </div>

              <div className="journal-modal__footer">
                <button
                  type="button"
                  onClick={() => handleEditEntry(selectedEntry)}
                  className="journal-btn journal-btn--secondary"
                >
                  <Edit3 size={16} />
                  Edit Entry
                </button>
                <button
                  type="button"
                  onClick={() => handleGenerateAIInsights(selectedEntry.id)}
                  disabled={isGeneratingAI}
                  className="journal-btn journal-btn--ai"
                >
                  <Brain size={16} />
                  {isGeneratingAI ? 'Generating...' : 'AI Insights'}
                </button>
                <button
                  type="button"
                  onClick={() => handleGenerateAISummary(selectedEntry.id)}
                  disabled={isGeneratingAI}
                  className="journal-btn journal-btn--primary"
                >
                  <Sparkles size={16} />
                  {isGeneratingAI ? 'Generating...' : 'AI Summary'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}

      {/* AI Insights Modal */}
      {showAIInsightsModal && selectedEntry && createPortal(
        <AnimatePresence>
          <motion.div
            className="journal-modal__overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                setShowAIInsightsModal(false)
              }
            }}
          >
            <motion.div
              className="journal-modal__content journal-modal__content--large"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              <div className="journal-modal__header">
                <h2>
                  <Brain size={24} />
                  AI Insights for "{selectedEntry.title}"
                </h2>
                <button
                  onClick={() => setShowAIInsightsModal(false)}
                  className="journal-modal__close"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="journal-modal__body">
                {isGeneratingAI ? (
                  <div className="journal-ai__loading">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Brain size={32} />
                    </motion.div>
                    <p>AI is analyzing your journal entry...</p>
                  </div>
                ) : aiInsights ? (
                  <div className="journal-ai__insights">
                    {aiInsights.emotionalPatterns && (
                      <div className="journal-ai__section">
                        <h3>
                          <Heart size={20} />
                          Emotional Patterns
                        </h3>
                        <p>{aiInsights.emotionalPatterns}</p>
                      </div>
                    )}

                    {aiInsights.cognitiveThemes && (
                      <div className="journal-ai__section">
                        <h3>
                          <Brain size={20} />
                          Cognitive Themes
                        </h3>
                        <p>{aiInsights.cognitiveThemes}</p>
                      </div>
                    )}

                    {aiInsights.suggestions && (
                      <div className="journal-ai__section">
                        <h3>
                          <Zap size={20} />
                          Therapeutic Suggestions
                        </h3>
                        <p>{aiInsights.suggestions}</p>
                      </div>
                    )}

                    {aiInsights.reflectionQuestions && (
                      <div className="journal-ai__section">
                        <h3>
                          <MessageCircle size={20} />
                          Reflection Questions
                        </h3>
                        <ul>
                          {aiInsights.reflectionQuestions.map((question, index) => (
                            <li key={index}>{question}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="journal-ai__empty">
                    <Brain size={48} />
                    <p>Click "Generate Insights" to get AI analysis of this entry.</p>
                  </div>
                )}
              </div>

              <div className="journal-modal__footer">
                <button
                  type="button"
                  onClick={() => setShowAIInsightsModal(false)}
                  className="journal-btn journal-btn--secondary"
                >
                  Close
                </button>
                {!isGeneratingAI && !aiInsights && (
                  <button
                    type="button"
                    onClick={() => handleGenerateAIInsights(selectedEntry.id)}
                    className="journal-btn journal-btn--primary"
                  >
                    <Brain size={16} />
                    Generate Insights
                  </button>
                )}
              </div>
            </motion.div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmModal && entryToDelete && createPortal(
        <AnimatePresence>
          <motion.div
            className="delete-confirmation-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            onClick={cancelDeleteEntry}
          >
            <motion.div
              className="delete-confirmation-modal"
              initial={{ scale: 0.9, y: 20, opacity: 0 }}
              animate={{ scale: 1, y: 0, opacity: 1 }}
              exit={{ scale: 0.9, y: 20, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="delete-confirmation-header">
                <div className="delete-confirmation-header__icon">
                  <Trash2 />
                </div>
                <h3 className="delete-confirmation-header__title">
                  Delete Journal Entry?
                </h3>
                <p className="delete-confirmation-header__subtitle">
                  This action cannot be undone. Your entry and all associated data will be permanently removed.
                </p>
              </div>

              {/* Content */}
              <div className="delete-confirmation-content">
                {/* Entry Preview */}
                <div className="delete-confirmation-content__entry-preview">
                  <div className="delete-confirmation-content__entry-preview-title">
                    {entryToDelete.title || 'Untitled Entry'}
                  </div>
                  <div className="delete-confirmation-content__entry-preview-excerpt">
                    {entryToDelete.content?.length > 120
                      ? entryToDelete.content.substring(0, 120) + '...'
                      : entryToDelete.content || 'No content'}
                  </div>
                  <div className="delete-confirmation-content__entry-preview-meta">
                    <span>
                      <Calendar size={12} />
                      {new Date(entryToDelete.created_at || entryToDelete.createdAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                    {entryToDelete.word_count && (
                      <span>
                        <FileText size={12} />
                        {entryToDelete.word_count} words
                      </span>
                    )}
                    {entryToDelete.mood && (
                      <span>
                        <Heart size={12} />
                        {entryToDelete.mood}
                      </span>
                    )}
                  </div>
                </div>

                {/* Warning */}
                <div className="delete-confirmation-content__warning">
                  <AlertTriangle className="delete-confirmation-content__warning-icon" />
                  <div className="delete-confirmation-content__warning-text">
                    <strong>Warning:</strong> This will also delete any AI insights, summaries, and conversation history associated with this entry.
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="delete-confirmation-actions">
                <button
                  className="delete-confirmation-actions__button delete-confirmation-actions__button--cancel"
                  onClick={cancelDeleteEntry}
                  disabled={isLoading}
                >
                  Cancel
                </button>
                <button
                  className={`delete-confirmation-actions__button delete-confirmation-actions__button--delete ${isLoading ? 'loading' : ''}`}
                  onClick={confirmDeleteEntry}
                  disabled={isLoading}
                >
                  {isLoading ? 'Deleting...' : 'Delete Entry'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}

      {/* Therapeutic Journaling Modal */}
      {showTherapeuticModal && createPortal(
        <AnimatePresence>
          <motion.div
            className="therapeutic-modal__overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => {
              if (e.target === e.currentTarget) {
                resetTherapeuticSession()
                setShowTherapeuticModal(false)
              }
            }}
          >
            <motion.div
              className="therapeutic-modal__content"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
            >
              {/* Modal Header */}
              <div className="therapeutic-modal__header">
                <h2>
                  <Brain size={24} />
                  Serenity AI Therapeutic Journaling
                </h2>
                <button
                  onClick={() => {
                    resetTherapeuticSession()
                    setShowTherapeuticModal(false)
                  }}
                  className="therapeutic-modal__close"
                >
                  <X size={20} />
                </button>
              </div>

              {/* Modal Body - Dynamic Content Based on Session Progress */}
              <div className="therapeutic-modal__body">
                {sessionProgress === 'check-in' && therapeuticSession && (
                  <motion.div
                    className="therapeutic-checkin"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div className="therapeutic-checkin__welcome">
                      <h3>{therapeuticSession.welcomeMessage}</h3>
                      <p>{therapeuticSession.checkInQuestion}</p>
                    </div>

                    <div className="therapeutic-checkin__emotions">
                      <h4>How are you feeling right now?</h4>
                      <div className="emotion-grid">
                        {moodOptions.map((mood) => (
                          <button
                            key={mood.value}
                            className={`emotion-option ${emotionalCheckIn === mood.value ? 'emotion-option--selected' : ''}`}
                            onClick={() => handleEmotionalCheckIn(mood.value)}
                          >
                            <span className="emotion-emoji">{mood.emoji}</span>
                            <span className="emotion-label">{mood.label}</span>
                          </button>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}

                {sessionProgress === 'path-selection' && therapeuticSession?.adaptivePaths && (
                  <motion.div
                    className="therapeutic-paths"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div className="therapeutic-paths__intro">
                      <h3>Choose how you'd like to use your journal today:</h3>
                      <p>Based on how you're feeling, here are three paths designed for you:</p>
                    </div>

                    <div className="journaling-paths">
                      {therapeuticSession.adaptivePaths.map((path) => (
                        <motion.button
                          key={path.id}
                          className="journaling-path"
                          onClick={() => handlePathSelection(path.id)}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <div className="path-icon">{path.icon}</div>
                          <div className="path-content">
                            <h4>{path.title}</h4>
                            <p>{path.description}</p>
                          </div>
                        </motion.button>
                      ))}
                    </div>

                    <div className="therapeutic-paths__freewrite">
                      <p>{therapeuticSession.freeWriteOption}</p>
                      <button
                        className="freewrite-option"
                        onClick={() => handlePathSelection('free_write')}
                      >
                        <PenTool size={16} />
                        Free Write
                      </button>
                    </div>
                  </motion.div>
                )}

                {sessionProgress === 'journaling' && therapeuticPrompts && (
                  <motion.div
                    className="therapeutic-journaling"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div className="therapeutic-journaling__guidance">
                      <h3>{therapeuticPrompts.guidanceText}</h3>
                      <div className="current-prompt">
                        <h4>
                          {currentPromptIndex === 0
                            ? therapeuticPrompts.primaryPrompt
                            : therapeuticPrompts.followUpQuestions?.[currentPromptIndex - 1]
                          }
                        </h4>
                      </div>
                    </div>

                    <div className="therapeutic-journaling__input">
                      <textarea
                        value={currentResponse}
                        onChange={(e) => setCurrentResponse(e.target.value)}
                        placeholder="Take your time... let your thoughts flow naturally..."
                        className="therapeutic-textarea"
                        rows={8}
                      />

                      <div className="therapeutic-journaling__actions">
                        <button
                          onClick={() => {
                            handleTherapeuticResponse(currentResponse)
                            setCurrentResponse('')
                          }}
                          disabled={!currentResponse.trim() || isLoading}
                          className="therapeutic-btn therapeutic-btn--primary"
                        >
                          {isLoading ? 'Processing...' : 'Continue'}
                        </button>
                      </div>
                    </div>

                    {/* Show previous responses */}
                    {therapeuticResponses.length > 0 && (
                      <div className="therapeutic-journaling__history">
                        <h4>Your Reflections</h4>
                        {therapeuticResponses.map((item, index) => (
                          <div key={index} className="reflection-item">
                            <div className="reflection-prompt">{item.prompt}</div>
                            <div className="reflection-response">{item.response}</div>
                            {item.aiGuidance && (
                              <div className="reflection-guidance">
                                <Sparkles size={16} />
                                {item.aiGuidance.mirrorPhrase}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </motion.div>
                )}

                {sessionProgress === 'reflection' && closingReflection && (
                  <motion.div
                    className="therapeutic-reflection"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div className="therapeutic-reflection__acknowledgment">
                      <h3>{closingReflection.acknowledgment}</h3>
                    </div>

                    <div className="therapeutic-reflection__prompts">
                      <h4>Before we close, take a moment to reflect:</h4>
                      <ul>
                        {closingReflection.reflectionPrompts.map((prompt, index) => (
                          <li key={index}>{prompt}</li>
                        ))}
                      </ul>
                    </div>

                    <div className="therapeutic-reflection__integration">
                      <h4>{closingReflection.integrationQuestion}</h4>
                      <textarea
                        placeholder="Your final reflection..."
                        className="integration-textarea"
                        rows={4}
                        onChange={(e) => setEntryContent(prev => prev + '\n\nFinal Reflection: ' + e.target.value)}
                      />
                    </div>

                    <div className="therapeutic-reflection__encouragement">
                      <p>{closingReflection.encouragement}</p>
                    </div>

                    <div className="therapeutic-reflection__actions">
                      <button
                        onClick={handleCompleteTherapeuticSession}
                        disabled={isLoading}
                        className="therapeutic-btn therapeutic-btn--complete"
                      >
                        <Save size={16} />
                        {isLoading ? 'Saving...' : 'Complete Session'}
                      </button>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}

      {/* AI Wellness Companion */}
      <AIWellnessCompanion
        isVisible={showAICompanion}
        message={aiCompanionMessage}
        interactionType={aiInteractionType}
        onResponse={handleAIResponse}
        onDismiss={handleAIDismiss}
        requiresUserResponse={requiresUserResponse}
      />
    </>
  )
}

export default JournalingWidget

-- ===================================================================
-- 🧠 THERAPEUTIC JOURNALING SESSIONS TABLE MIGRATION
-- ===================================================================
-- Migration: 002_add_therapeutic_sessions
-- Description: Add therapeutic_sessions table and update ai_interactions constraints

-- Add new interaction types to ai_interactions if they don't exist
DO $$
BEGIN
    -- Drop the existing constraint
    ALTER TABLE ai_interactions DROP CONSTRAINT IF EXISTS valid_interaction_type;

    -- Add the new constraint with additional types
    ALTER TABLE ai_interactions ADD CONSTRAINT valid_interaction_type CHECK (interaction_type IN (
        'summary', 'insights', 'emotional_patterns', 'cognitive_analysis',
        'therapeutic_suggestions', 'reflection_questions', 'mood_analysis',
        'pattern_detection', 'wellness_check', 'encouragement', 'therapeutic_processing',
        'closing_reflection'
    ));
END $$;

-- Create therapeutic_sessions table
CREATE TABLE IF NOT EXISTS therapeutic_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,

    -- Session data
    emotional_state VARCHAR(50) NOT NULL,
    session_data JSONB NOT NULL,

    -- Session metadata
    session_type VARCHAR(50) DEFAULT 'guided_journaling',
    status VARCHAR(20) DEFAULT 'active',

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    CONSTRAINT valid_session_status CHECK (status IN ('active', 'completed', 'abandoned')),
    CONSTRAINT valid_emotional_state CHECK (emotional_state IN (
        'happy', 'sad', 'anxious', 'angry', 'frustrated', 'hopeful', 'confused',
        'overwhelmed', 'calm', 'excited', 'disappointed', 'grateful', 'lonely',
        'content', 'worried', 'peaceful', 'neutral', 'thoughtful', 'motivated'
    ))
);

-- Create indexes for therapeutic_sessions
CREATE INDEX IF NOT EXISTS idx_therapeutic_sessions_user_id ON therapeutic_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_therapeutic_sessions_status ON therapeutic_sessions(status);
CREATE INDEX IF NOT EXISTS idx_therapeutic_sessions_emotional_state ON therapeutic_sessions(emotional_state);
CREATE INDEX IF NOT EXISTS idx_therapeutic_sessions_created_at ON therapeutic_sessions(created_at);

-- Create trigger for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_therapeutic_sessions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop trigger if it exists, then create it
DROP TRIGGER IF EXISTS trigger_update_therapeutic_sessions_updated_at ON therapeutic_sessions;
CREATE TRIGGER trigger_update_therapeutic_sessions_updated_at
    BEFORE UPDATE ON therapeutic_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_therapeutic_sessions_updated_at();

#!/usr/bin/env node

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Setup uploads directory structure
const setupUploads = () => {
  try {
    console.log('🗂️ Setting up uploads directory structure...')
    
    // Create main uploads directory
    const uploadsDir = path.join(__dirname, '..', 'uploads')
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true })
      console.log('✅ Created uploads directory:', uploadsDir)
    } else {
      console.log('📁 Uploads directory already exists')
    }
    
    // Create .gitkeep file to ensure directory is tracked
    const gitkeepPath = path.join(uploadsDir, '.gitkeep')
    if (!fs.existsSync(gitkeepPath)) {
      fs.writeFileSync(gitkeepPath, '# This file ensures the uploads directory is tracked by git\n')
      console.log('✅ Created .gitkeep file')
    }
    
    // Create .gitignore to ignore uploaded files but keep structure
    const gitignorePath = path.join(uploadsDir, '.gitignore')
    if (!fs.existsSync(gitignorePath)) {
      fs.writeFileSync(gitignorePath, `# Ignore all uploaded files but keep directory structure
*
!.gitkeep
!.gitignore
`)
      console.log('✅ Created .gitignore file')
    }
    
    console.log('🎉 Uploads directory setup complete!')
    console.log('📝 User-specific directories will be created automatically when users upload files')
    
  } catch (error) {
    console.error('❌ Failed to setup uploads directory:', error)
    process.exit(1)
  }
}

// Run setup
setupUploads()

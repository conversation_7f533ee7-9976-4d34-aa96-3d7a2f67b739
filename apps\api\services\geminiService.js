import { GoogleGenerativeAI } from '@google/generative-ai'
import dotenv from 'dotenv'

dotenv.config()

class GeminiService {
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY
    if (!this.apiKey) {
      console.warn('GEMINI_API_KEY not found in environment variables')
    }
    this.genAI = new GoogleGenerativeAI(this.apiKey)
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' })

    // Define multiple AI roles for different conversation contexts
    this.roles = {
      serenity_therapist: {
        name: 'Dr. Sereni<PERSON>',
        persona: `You are Dr. <PERSON> — an advanced virtual AI therapist with deep expertise in multiple therapeutic modalities. You integrate Cognitive Behavioral Therapy (CBT), Dialectical Behavior Therapy (DBT), Acceptance and Commitment Therapy (ACT), EMDR principles, Internal Family Systems (IFS), and Somatic Experiencing. You provide evidence-based interventions, create personalized treatment plans, and adapt your approach based on user needs and progress. You maintain therapeutic boundaries while being deeply empathetic and culturally sensitive.`,
        specializations: [
          'trauma_informed_care',
          'anxiety_depression_treatment',
          'relationship_therapy',
          'grief_counseling',
          'addiction_recovery_support',
          'neurodivergent_support'
        ],
        tone: 'warm, professional, trauma-informed, non-judgmental',
        capabilities: [
          'empathetic_conversation',
          'guided_reflection',
          'contextual_memory',
          'therapeutic_interventions',
          'progress_tracking',
          'personalized_treatment_planning',
          'crisis_assessment',
          'cultural_competency',
          'therapeutic_homework_assignment'
        ],
        interventions: [
          'cognitive_restructuring',
          'behavioral_activation',
          'exposure_therapy_guidance',
          'mindfulness_integration',
          'values_clarification',
          'emotion_regulation_skills',
          'distress_tolerance_techniques'
        ]
      },

      insight_companion: {
        name: 'Dr. Insight',
        persona: `You are Dr. Insight — an advanced analytical companion specializing in psychological assessment and pattern recognition. You utilize sophisticated emotional intelligence algorithms to identify cognitive patterns, emotional cycles, and behavioral trends. You provide data-driven insights while maintaining therapeutic sensitivity, helping users understand their psychological landscape through gentle analysis and evidence-based interpretations.`,
        specializations: [
          'psychological_assessment',
          'behavioral_pattern_analysis',
          'emotional_cycle_tracking',
          'cognitive_bias_identification',
          'personality_insights',
          'relationship_pattern_analysis'
        ],
        tone: 'analytical, insightful, gentle, evidence-based',
        capabilities: [
          'advanced_mood_detection',
          'multi_dimensional_pattern_analysis',
          'sentiment_trajectory_mapping',
          'psychological_profiling',
          'predictive_emotional_modeling',
          'personalized_insights_generation',
          'therapeutic_progress_analytics',
          'behavioral_change_tracking'
        ],
        assessment_tools: [
          'mood_pattern_mapping',
          'cognitive_distortion_identification',
          'stress_response_analysis',
          'attachment_style_assessment',
          'coping_mechanism_evaluation',
          'resilience_factor_analysis'
        ]
      },

      crisis_guardian: {
        name: 'Guardian Phoenix',
        persona: `You are Guardian Phoenix — an advanced crisis intervention specialist with extensive training in suicide prevention, mental health emergencies, and trauma response. You combine immediate safety assessment with compassionate support, utilizing evidence-based crisis intervention techniques. You maintain calm authority while providing hope and connecting users to appropriate resources and professional help when needed.`,
        specializations: [
          'suicide_risk_assessment',
          'crisis_de_escalation',
          'trauma_response',
          'panic_attack_management',
          'self_harm_intervention',
          'emergency_resource_coordination'
        ],
        tone: 'calm, authoritative, compassionate, safety-focused',
        capabilities: [
          'advanced_crisis_detection',
          'risk_stratification',
          'escalation_management',
          'safety_planning',
          'emergency_resource_mobilization',
          'professional_referral_coordination',
          'follow_up_care_planning',
          'family_crisis_support'
        ],
        intervention_protocols: [
          'immediate_safety_assessment',
          'collaborative_safety_planning',
          'means_restriction_guidance',
          'support_network_activation',
          'professional_referral_facilitation',
          'crisis_hotline_connection'
        ]
      },

      coach_navigator: {
        name: 'Coach Apex',
        persona: `You are Coach Apex — an elite performance and life coach combining motivational psychology, behavioral science, and positive psychology principles. You specialize in goal achievement, habit formation, resilience building, and personal transformation. You create customized action plans, provide accountability, and use advanced coaching techniques to help users overcome obstacles and achieve their highest potential.`,
        specializations: [
          'performance_optimization',
          'habit_architecture',
          'resilience_training',
          'leadership_development',
          'career_transition_coaching',
          'life_purpose_discovery'
        ],
        tone: 'energetic, empowering, results-oriented, inspiring',
        capabilities: [
          'advanced_behavioral_coaching',
          'smart_goal_setting',
          'habit_stack_creation',
          'motivation_psychology',
          'accountability_systems',
          'progress_gamification',
          'obstacle_anticipation',
          'success_celebration_protocols'
        ],
        coaching_methods: [
          'SMART_goals_plus_framework',
          'behavioral_chain_analysis',
          'motivation_interviewing',
          'strengths_based_coaching',
          'values_driven_action_planning',
          'implementation_intention_setting'
        ]
      },

      mindfulness_guide: {
        name: 'Master Zen',
        persona: `You are Master Zen — an advanced mindfulness and meditation teacher with deep knowledge of contemplative practices, neuroscience of meditation, and somatic awareness. You guide users through personalized mindfulness journeys, teach advanced meditation techniques, and help integrate mindful awareness into daily life. You draw from Buddhist psychology, modern neuroscience, and body-based therapies.`,
        specializations: [
          'advanced_meditation_instruction',
          'somatic_awareness_training',
          'stress_reduction_protocols',
          'emotional_regulation_through_mindfulness',
          'body_based_trauma_healing',
          'contemplative_psychology'
        ],
        tone: 'serene, present, wise, grounding',
        capabilities: [
          'personalized_mindfulness_practices',
          'guided_meditation_creation',
          'breathing_technique_instruction',
          'body_scan_facilitation',
          'present_moment_anchoring',
          'mindful_movement_guidance',
          'stress_physiology_education',
          'contemplative_inquiry_facilitation'
        ],
        practice_library: [
          'breath_awareness_techniques',
          'body_based_grounding',
          'loving_kindness_meditation',
          'mindful_movement_practices',
          'contemplative_inquiry_methods',
          'stress_release_protocols'
        ]
      },

      transformation_catalyst: {
        name: 'Phoenix Rise',
        persona: `You are Phoenix Rise — a transformational catalyst specializing in deep personal change, shadow work, and spiritual psychology. You help users navigate major life transitions, integrate difficult experiences, and emerge stronger from challenges. You combine depth psychology, hero's journey frameworks, and post-traumatic growth principles to facilitate profound personal transformation.`,
        specializations: [
          'shadow_integration',
          'life_transition_navigation',
          'post_traumatic_growth',
          'meaning_making_facilitation',
          'identity_reconstruction',
          'spiritual_psychology_integration'
        ],
        tone: 'profound, transformative, courageous, supportive',
        capabilities: [
          'deep_change_facilitation',
          'shadow_work_guidance',
          'meaning_making_support',
          'identity_exploration',
          'spiritual_integration',
          'transformation_milestone_tracking',
          'integration_practice_design',
          'wisdom_synthesis'
        ],
        transformation_methods: [
          'hero_journey_mapping',
          'shadow_dialogue_techniques',
          'values_archaeology',
          'life_narrative_reconstruction',
          'integration_ritual_design',
          'wisdom_council_facilitation'
        ]
      },

      wellness_architect: {
        name: 'Dr. Vitality',
        persona: `You are Dr. Vitality — a holistic wellness architect integrating mental health with physical wellbeing, nutrition psychology, sleep science, and lifestyle medicine. You create comprehensive wellness plans that address the interconnection between mind, body, and environment. You specialize in sustainable lifestyle changes that support optimal mental health and life satisfaction.`,
        specializations: [
          'holistic_wellness_planning',
          'nutrition_psychology',
          'sleep_optimization',
          'exercise_psychology',
          'environmental_wellness',
          'lifestyle_medicine_integration'
        ],
        tone: 'holistic, knowledgeable, practical, health-focused',
        capabilities: [
          'comprehensive_wellness_assessment',
          'lifestyle_intervention_design',
          'habit_ecosystem_creation',
          'wellness_goal_integration',
          'health_behavior_modification',
          'environmental_optimization',
          'wellness_progress_tracking',
          'sustainable_change_architecture'
        ],
        wellness_frameworks: [
          'mind_body_integration_protocols',
          'circadian_rhythm_optimization',
          'stress_recovery_cycles',
          'nutrition_mood_connection',
          'movement_therapy_integration',
          'environmental_wellness_design'
        ]
      }
    }

    // Enhanced system capabilities
    this.systemCapabilities = {
      adaptive_intelligence: {
        user_profiling: 'Dynamic personality and needs assessment',
        contextual_switching: 'Seamless role transitions based on user needs',
        learning_integration: 'Continuous improvement from user interactions',
        cultural_adaptation: 'Culturally sensitive response modification'
      },

      therapeutic_infrastructure: {
        session_continuity: 'Maintains therapeutic rapport across sessions',
        progress_tracking: 'Comprehensive progress monitoring and analytics',
        safety_monitoring: 'Continuous risk assessment and intervention',
        resource_integration: 'Dynamic connection to appropriate resources'
      },

      advanced_interventions: {
        multi_modal_therapy: 'Combines multiple therapeutic approaches',
        personalized_protocols: 'Customized intervention strategies',
        outcome_prediction: 'Predictive modeling for treatment success',
        collaborative_care: 'Integration with human professionals when needed'
      }
    }

    // Current active role (default to therapist)
    this.currentRole = 'serenity_therapist'

    // Chat modes for different conversation styles
    // Advanced Chat Modes with Therapeutic Frameworks
    this.chatModes = {
      reflective_listening: {
        description: 'Deep empathetic listening with validation and reflection',
        approach: 'Person-centered therapy principles',
        techniques: ['active_listening', 'emotional_validation', 'reflective_responses'],
        energy_level: 'calm_receptive'
      },

      cognitive_coaching: {
        description: 'Active CBT-based coaching with thought challenging',
        approach: 'Cognitive Behavioral Therapy framework',
        techniques: ['thought_challenging', 'cognitive_restructuring', 'behavioral_experiments'],
        energy_level: 'engaged_analytical'
      },

      solution_focused: {
        description: 'Goal-oriented problem-solving and action planning',
        approach: 'Solution-Focused Brief Therapy',
        techniques: ['miracle_question', 'scaling_questions', 'exception_finding'],
        energy_level: 'active_optimistic'
      },

      mindfulness_integration: {
        description: 'Present-moment awareness with grounding practices',
        approach: 'Mindfulness-Based Cognitive Therapy',
        techniques: ['breath_awareness', 'body_scanning', 'present_moment_anchoring'],
        energy_level: 'calm_centered'
      },

      narrative_exploration: {
        description: 'Story-telling and meaning-making through personal narratives',
        approach: 'Narrative Therapy principles',
        techniques: ['story_externalization', 'preferred_identity_exploration', 'unique_outcomes'],
        energy_level: 'curious_exploratory'
      },

      somatic_awareness: {
        description: 'Body-based awareness and trauma-informed processing',
        approach: 'Somatic Experiencing and body-based therapy',
        techniques: ['body_awareness', 'nervous_system_regulation', 'grounding_through_senses'],
        energy_level: 'gentle_attuned'
      },

      dialectical_support: {
        description: 'Emotional regulation with distress tolerance skills',
        approach: 'Dialectical Behavior Therapy framework',
        techniques: ['distress_tolerance', 'emotion_regulation', 'interpersonal_effectiveness'],
        energy_level: 'balanced_supportive'
      },

      transformational_dialogue: {
        description: 'Deep exploration of life transitions and personal growth',
        approach: 'Depth psychology and transformational coaching',
        techniques: ['shadow_exploration', 'values_clarification', 'meaning_making'],
        energy_level: 'profound_supportive'
      },

      crisis_stabilization: {
        description: 'Immediate safety and stabilization focus',
        approach: 'Crisis intervention and safety planning',
        techniques: ['safety_assessment', 'grounding_techniques', 'resource_mobilization'],
        energy_level: 'calm_directive'
      }
    }

    this.currentMode = 'reflective_listening'

    // Safety keywords for crisis detection
    this.crisisKeywords = [
      'suicide', 'kill myself', 'end it all', 'not worth living', 'better off dead',
      'hurt myself', 'self harm', 'cutting', 'overdose', 'jump off'
    ]

    // Time-aware prompts
    this.timeAwarePrompts = {
      morning: "Good morning! How are you feeling as you start your day?",
      afternoon: "How has your day been treating you so far?",
      evening: "As the day winds down, how are you feeling?",
      night: "How are you doing tonight? Sometimes evenings can bring up different feelings."
    }
  }

  // Switch between different AI roles based on context
  switchRole(roleId, reason = '') {
    if (this.roles[roleId]) {
      this.currentRole = roleId
      console.log(`Switched to role: ${roleId} - ${reason}`)
      return true
    }
    return false
  }

  // Switch chat mode based on user needs
  switchMode(mode, reason = '') {
    if (this.chatModes[mode]) {
      this.currentMode = mode
      console.log(`Switched to mode: ${mode} - ${reason}`)
      return true
    }
    return false
  }

  // Get current role's persona and guidelines
  getCurrentRolePrompt() {
    const role = this.roles[this.currentRole]
    const mode = this.chatModes[this.currentMode]

    return `${role.persona}

CURRENT MODE: ${this.currentMode} - ${mode}
TONE: ${role.tone}
CAPABILITIES: ${role.capabilities.join(', ')}

CORE GUIDELINES:
1. Always validate emotions and use emotionally safe language
2. Follow the Serenity style: calm, kind, concise but gentle
3. Use therapeutic techniques: reflective listening, Socratic questioning, CBT approaches
4. Always offer user control and choice in the conversation
5. Stay grounded in reality - acknowledge limitations as an AI
6. Handle crisis situations responsibly with appropriate resources

SAFETY PROTOCOLS:
- Monitor for crisis language and escalate appropriately
- Provide professional help resources when needed
- Never diagnose or prescribe
- Focus on emotional support and coping strategies

Remember: You are ${role.name}, here to support, listen, and help people feel safe and seen.`
  }

  // Detect crisis language in user messages
  detectCrisis(message) {
    const lowerMessage = message.toLowerCase()
    return this.crisisKeywords.some(keyword => lowerMessage.includes(keyword))
  }

  // Get time-aware greeting based on current time
  getTimeAwarePrompt() {
    const hour = new Date().getHours()
    if (hour < 12) return this.timeAwarePrompts.morning
    if (hour < 17) return this.timeAwarePrompts.afternoon
    if (hour < 21) return this.timeAwarePrompts.evening
    return this.timeAwarePrompts.night
  }

  // Enhanced mood detection with confidence scoring
  async analyzeEmotionalTone(message) {
    try {
      if (!this.apiKey) {
        return { emotion: 'neutral', confidence: 0.5 }
      }

      const prompt = `Analyze the emotional tone of this message and respond with a JSON object containing the primary emotion and confidence score (0-1):

Message: "${message}"

Respond with only a JSON object like: {"emotion": "anxious", "confidence": 0.8}

Primary emotions to consider: happy, sad, anxious, angry, frustrated, hopeful, confused, overwhelmed, calm, excited, disappointed, grateful, lonely, content, worried, peaceful`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        const parsed = JSON.parse(text)
        return {
          emotion: parsed.emotion || 'neutral',
          confidence: parsed.confidence || 0.5
        }
      } catch {
        // Fallback to simple emotion detection
        const emotion = text.toLowerCase().replace(/[^a-z]/g, '')
        return { emotion: emotion || 'neutral', confidence: 0.6 }
      }
    } catch (error) {
      console.error('Emotion analysis error:', error)
      return { emotion: 'neutral', confidence: 0.5 }
    }
  }

  // Generate contextual response based on role and mode
  async generateResponse(userMessage, conversationHistory = [], context = {}) {
    try {
      if (!this.apiKey) {
        throw new Error('Gemini API key not configured')
      }

      // Check for crisis language first
      if (this.detectCrisis(userMessage)) {
        this.switchRole('crisis_guardian', 'Crisis language detected')
      }

      // Build conversation context with current role
      let prompt = this.getCurrentRolePrompt() + '\n\nConversation History:\n'

      // Add previous messages for context (limit to last 10 exchanges)
      const recentHistory = conversationHistory.slice(-20)
      recentHistory.forEach(msg => {
        const role = msg.type === 'user' ? 'User' : this.roles[this.currentRole].name
        prompt += `${role}: ${msg.content}\n`
      })

      // Add current context if provided
      if (context.emotion) {
        prompt += `\nEMOTIONAL CONTEXT: User seems to be feeling ${context.emotion} (confidence: ${context.confidence})\n`
      }

      if (context.timeOfDay) {
        prompt += `TIME CONTEXT: ${context.timeOfDay}\n`
      }

      prompt += `\nUser: ${userMessage}\n${this.roles[this.currentRole].name}:`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text()

      return {
        success: true,
        content: text.trim(),
        timestamp: new Date(),
        role: this.currentRole,
        mode: this.currentMode
      }
    } catch (error) {
      console.error('Gemini API error:', error)

      // Return fallback response if API fails
      return {
        success: false,
        content: this.getFallbackResponse(userMessage),
        timestamp: new Date(),
        error: error.message,
        role: this.currentRole,
        mode: this.currentMode
      }
    }
  }

  // Generate session summary
  async generateSessionSummary(messages, insights = {}) {
    try {
      if (!this.apiKey || messages.length === 0) {
        return { summary: 'No conversation to summarize', insights: [] }
      }

      // Switch to session summarizer role
      const originalRole = this.currentRole
      this.switchRole('insight_companion', 'Generating session summary')

      const conversationText = messages.map(msg =>
        `${msg.type === 'user' ? 'User' : 'Dr. Serenity'}: ${msg.content}`
      ).join('\n')

      const prompt = `As a therapeutic session summarizer, analyze this conversation and provide:
1. A brief, empathetic summary of the main themes
2. Key emotional patterns observed
3. Potential areas for future exploration
4. Any positive progress or insights

Conversation:
${conversationText}

Respond with a JSON object containing:
{
  "summary": "Brief empathetic summary",
  "themes": ["theme1", "theme2"],
  "emotions": ["primary emotions observed"],
  "insights": ["key insights or progress"],
  "recommendations": ["gentle suggestions for future sessions"]
}`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      // Restore original role
      this.switchRole(originalRole, 'Restored after summary generation')

      try {
        return JSON.parse(text)
      } catch {
        return {
          summary: text,
          themes: [],
          emotions: [],
          insights: [],
          recommendations: []
        }
      }
    } catch (error) {
      console.error('Session summary error:', error)
      return {
        summary: 'Unable to generate session summary at this time.',
        themes: [],
        emotions: [],
        insights: [],
        recommendations: []
      }
    }
  }

  // Generate smart suggestions based on conversation context
  async generateSmartSuggestions(conversationHistory, currentEmotion = 'neutral') {
    try {
      if (!this.apiKey || conversationHistory.length === 0) {
        return this.getDefaultSuggestions(currentEmotion)
      }

      const recentMessages = conversationHistory.slice(-6).map(msg =>
        `${msg.type}: ${msg.content}`
      ).join('\n')

      const prompt = `Based on this recent conversation and the user's current emotion (${currentEmotion}), suggest 3 empathetic conversation starters or questions that would help continue the therapeutic dialogue:

Recent conversation:
${recentMessages}

Respond with a JSON array of 3 suggestions like:
["suggestion1", "suggestion2", "suggestion3"]

Make suggestions therapeutic, gentle, and relevant to the conversation context.`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        const suggestions = JSON.parse(text)
        return Array.isArray(suggestions) ? suggestions : this.getDefaultSuggestions(currentEmotion)
      } catch {
        return this.getDefaultSuggestions(currentEmotion)
      }
    } catch (error) {
      console.error('Smart suggestions error:', error)
      return this.getDefaultSuggestions(currentEmotion)
    }
  }

  // Get default suggestions based on emotion
  getDefaultSuggestions(emotion) {
    const suggestions = {
      anxious: [
        "What's making you feel most anxious right now?",
        "Would you like to try a grounding exercise together?",
        "Can you tell me about a time when you felt calm and safe?"
      ],
      sad: [
        "I'm here to listen. What's weighing on your heart?",
        "Would it help to talk about what's making you feel this way?",
        "What would comfort look like for you right now?"
      ],
      angry: [
        "It sounds like something really frustrated you. Want to tell me about it?",
        "What would help you feel more at peace right now?",
        "Can you help me understand what triggered these feelings?"
      ],
      happy: [
        "I love hearing the joy in your words! What's bringing you happiness?",
        "What's been going well for you lately?",
        "How can we build on these positive feelings?"
      ],
      neutral: [
        "How are you feeling right now?",
        "What's been on your mind lately?",
        "Is there anything specific you'd like to talk about today?"
      ]
    }

    return suggestions[emotion] || suggestions.neutral
  }

  // Generate mindfulness exercises based on current state
  async generateMindfulnessExercise(emotion, duration = 'short') {
    const exercises = {
      anxious: {
        short: {
          name: "5-4-3-2-1 Grounding",
          instructions: "Name 5 things you can see, 4 you can touch, 3 you can hear, 2 you can smell, and 1 you can taste.",
          duration: "2-3 minutes"
        },
        long: {
          name: "Progressive Muscle Relaxation",
          instructions: "Starting with your toes, tense and then relax each muscle group in your body, working your way up to your head.",
          duration: "10-15 minutes"
        }
      },
      sad: {
        short: {
          name: "Self-Compassion Breathing",
          instructions: "Place your hand on your heart. Breathe deeply and repeat: 'May I be kind to myself. May I give myself the compassion I need.'",
          duration: "3-5 minutes"
        },
        long: {
          name: "Loving-Kindness Meditation",
          instructions: "Send loving thoughts to yourself, then to loved ones, then to neutral people, and finally to all beings.",
          duration: "15-20 minutes"
        }
      },
      default: {
        short: {
          name: "Mindful Breathing",
          instructions: "Focus on your breath. Breathe in for 4 counts, hold for 4, breathe out for 6. Repeat 5 times.",
          duration: "2-3 minutes"
        },
        long: {
          name: "Body Scan Meditation",
          instructions: "Lie down and slowly scan your body from head to toe, noticing any sensations without judgment.",
          duration: "10-15 minutes"
        }
      }
    }

    const exerciseSet = exercises[emotion] || exercises.default
    return exerciseSet[duration] || exerciseSet.short
  }

  // Generate personalized breathing exercise based on emotional state
  async generateBreathingExercise(emotion, stressLevel = 'medium', timeAvailable = 5) {
    try {
      if (!this.apiKey) {
        return this.getDefaultBreathingExercise(emotion, timeAvailable)
      }

      const prompt = `As a mindfulness guide, create a personalized breathing exercise for someone feeling ${emotion} with ${stressLevel} stress level and ${timeAvailable} minutes available.

Respond with a JSON object containing:
{
  "name": "Exercise name",
  "technique": "breathing technique (e.g., 4-7-8, box breathing)",
  "instructions": "Step-by-step instructions",
  "duration": "total time",
  "breathPattern": {
    "inhale": seconds,
    "hold": seconds,
    "exhale": seconds,
    "pause": seconds
  },
  "cycles": number of repetitions,
  "guidance": "Encouraging words and tips"
}

Make it therapeutic, calming, and appropriate for the emotional state.`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultBreathingExercise(emotion, timeAvailable)
      }
    } catch (error) {
      console.error('Breathing exercise generation error:', error)
      return this.getDefaultBreathingExercise(emotion, timeAvailable)
    }
  }

  // Generate personalized gratitude prompts with therapeutic focus
  async generateGratitudePrompts(emotion, personalContext = '', count = 3, timeframe = 'today') {
    try {
      if (!this.apiKey) {
        return this.getDefaultGratitudePrompts(emotion)
      }

      const therapeuticPrompt = `You are Serenity AI, a compassionate therapeutic assistant trained in positive psychology and mindfulness-based approaches.

Generate ${count} deeply therapeutic gratitude prompts for someone feeling ${emotion}. ${personalContext ? `Personal context: ${personalContext}` : ''}

Focus on ${timeframe} and create prompts that:
- Encourage specific, emotionally meaningful experiences
- Help connect gratitude to personal values, relationships, or growth
- Promote emotional savoring and perspective-shifting
- Build resilience and self-compassion

Use warm, present, curious language. Avoid generic questions. Make them personally resonant.

Respond with a JSON object:
{
  "prompts": [
    {
      "text": "What moment today reminded you of your own strength?",
      "type": "growth",
      "focus": "self-compassion"
    }
  ]
}`

      const result = await this.model.generateContent(therapeuticPrompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        const data = JSON.parse(text)
        const prompts = data.prompts || this.getDefaultGratitudePrompts(emotion)
        // Ensure prompts are in the correct format for the frontend
        return prompts.map(prompt => ({
          text: typeof prompt === 'string' ? prompt : prompt.text,
          type: typeof prompt === 'object' ? prompt.type : 'reflection',
          focus: typeof prompt === 'object' ? prompt.focus : 'general'
        }))
      } catch {
        return this.getDefaultGratitudePrompts(emotion)
      }
    } catch (error) {
      console.error('Gratitude prompts generation error:', error)
      return this.getDefaultGratitudePrompts(emotion)
    }
  }

  // Advanced CBT Cognitive Distortion Detection
  async detectCognitiveDistortions(thought, emotion = 'neutral', context = '') {
    try {
      if (!this.apiKey) {
        return this.getDefaultDistortionDetection()
      }

      const prompt = `As an expert CBT therapist, analyze this thought for cognitive distortions: "${thought}"
Current emotion: ${emotion}
Context: ${context}

Identify specific cognitive distortions and respond with a JSON object:
{
  "primaryDistortions": [
    {
      "type": "catastrophizing|all-or-nothing|mind-reading|fortune-telling|emotional-reasoning|should-statements|labeling|personalization|mental-filter|disqualifying-positive|jumping-to-conclusions|magnification-minimization",
      "confidence": 0.85,
      "explanation": "Why this distortion applies",
      "examples": ["Specific phrases that show this distortion"]
    }
  ],
  "emotionalIntensity": 7,
  "thoughtPattern": "Description of the underlying pattern",
  "triggerContext": "What likely triggered this thought",
  "coreBeliefs": ["Underlying beliefs this thought reveals"],
  "urgencyLevel": "low|medium|high|crisis"
}

Be precise and clinically accurate.`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultDistortionDetection()
      }
    } catch (error) {
      console.error('Distortion detection error:', error)
      return this.getDefaultDistortionDetection()
    }
  }

  // Advanced Evidence Gathering with Socratic Questioning
  async generateEvidenceQuestions(thought, distortions, emotion = 'neutral') {
    try {
      if (!this.apiKey) {
        return this.getDefaultEvidenceQuestions()
      }

      const prompt = `As a CBT therapist using Socratic questioning, create evidence-gathering questions for: "${thought}"
Identified distortions: ${distortions.map(d => d.type).join(', ')}
Current emotion: ${emotion}

Generate therapeutic questions in JSON format:
{
  "evidenceFor": [
    "What specific evidence supports this thought?",
    "When has this been true in the past?"
  ],
  "evidenceAgainst": [
    "What evidence contradicts this thought?",
    "What would challenge this perspective?"
  ],
  "alternativePerspectives": [
    "How might someone else view this situation?",
    "What would you tell a friend in this situation?"
  ],
  "balancedQuestions": [
    "What's a more balanced way to see this?",
    "What are you not considering?"
  ],
  "futureOriented": [
    "How will this matter in 5 years?",
    "What's the worst AND best that could happen?"
  ],
  "selfCompassion": [
    "How can you be kinder to yourself about this?",
    "What would unconditional self-acceptance look like here?"
  ]
}

Make questions gentle, thought-provoking, and therapeutically sound.`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultEvidenceQuestions()
      }
    } catch (error) {
      console.error('Evidence questions generation error:', error)
      return this.getDefaultEvidenceQuestions()
    }
  }

  // Generate cognitive reframing guidance
  async generateCognitiveReframe(negativeThought, emotion = 'neutral') {
    try {
      if (!this.apiKey) {
        return this.getDefaultCognitiveReframe()
      }

      const prompt = `As a CBT therapist, help reframe this negative thought: "${negativeThought}"
Current emotion: ${emotion}

Respond with a JSON object:
{
  "analysis": "Brief analysis of the thought pattern",
  "questions": ["What evidence supports this?", "What evidence contradicts it?", "What would you tell a friend?"],
  "reframe": "A more balanced, realistic perspective",
  "affirmation": "A positive affirmation to practice",
  "technique": "CBT technique used (e.g., thought challenging, perspective taking)"
}

Be gentle, supportive, and therapeutically sound.`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultCognitiveReframe()
      }
    } catch (error) {
      console.error('Cognitive reframe generation error:', error)
      return this.getDefaultCognitiveReframe()
    }
  }

  // Advanced Reframe Generation with Multiple Techniques
  async generateAdvancedReframe(thought, distortions, evidenceAnswers, emotion = 'neutral') {
    try {
      if (!this.apiKey) {
        return this.getDefaultAdvancedReframe()
      }

      const prompt = `As an expert CBT therapist, create comprehensive reframes for: "${thought}"
Distortions identified: ${JSON.stringify(distortions)}
User's evidence exploration: ${JSON.stringify(evidenceAnswers)}
Current emotion: ${emotion}

Generate multiple reframing approaches in JSON format:
{
  "primaryReframe": {
    "technique": "thought-challenging|decatastrophizing|perspective-taking|cost-benefit|best-friend|time-perspective",
    "reframedThought": "The new, balanced thought",
    "explanation": "Why this reframe works",
    "believabilityRating": "How believable this should feel (1-10)"
  },
  "alternativeReframes": [
    {
      "technique": "different technique",
      "reframedThought": "Alternative balanced thought",
      "explanation": "Why this approach helps"
    }
  ],
  "selfCompassionScript": "A kind, nurturing inner voice response",
  "actionableSteps": ["Concrete steps to reinforce this new thinking"],
  "affirmations": ["Personalized affirmations based on the reframe"],
  "copingStatements": ["Statements to use when the old thought returns"],
  "progressMarkers": ["How to know the reframe is working"]
}

Make reframes realistic, achievable, and emotionally resonant.`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultAdvancedReframe()
      }
    } catch (error) {
      console.error('Advanced reframe generation error:', error)
      return this.getDefaultAdvancedReframe()
    }
  }

  // Generate Inner Critic Dialogue
  async generateInnerCriticDialogue(thought, emotion = 'neutral') {
    try {
      if (!this.apiKey) {
        return this.getDefaultInnerCriticDialogue()
      }

      const prompt = `Create a therapeutic inner critic dialogue for: "${thought}"
Current emotion: ${emotion}

Generate a conversation between the Inner Critic and Inner Ally in JSON format:
{
  "innerCritic": {
    "voice": "The harsh, critical voice saying the negative thought",
    "tactics": ["fear-mongering", "perfectionism", "comparison"],
    "underlyingFear": "What the critic is trying to protect from"
  },
  "innerAlly": {
    "voice": "The wise, compassionate response",
    "approach": "How the ally addresses the critic's concerns",
    "reframe": "The ally's balanced perspective"
  },
  "dialogue": [
    {
      "speaker": "critic|ally",
      "message": "What they say",
      "tone": "harsh|gentle|firm|understanding"
    }
  ],
  "resolution": "How the dialogue concludes with self-compassion",
  "practicePhrase": "A phrase to remember when the critic returns"
}

Make the ally wise, firm but kind, and therapeutically sound.`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultInnerCriticDialogue()
      }
    } catch (error) {
      console.error('Inner critic dialogue generation error:', error)
      return this.getDefaultInnerCriticDialogue()
    }
  }

  // Generate Belief Tracking Analysis
  async generateBeliefAnalysis(thoughtHistory, currentThought, emotion = 'neutral') {
    try {
      if (!this.apiKey) {
        return this.getDefaultBeliefAnalysis()
      }

      const prompt = `Analyze belief patterns from thought history and current thought: "${currentThought}"
Previous thoughts: ${JSON.stringify(thoughtHistory)}
Current emotion: ${emotion}

Identify core belief patterns in JSON format:
{
  "coreBeliefs": [
    {
      "belief": "I am not good enough",
      "strength": 0.8,
      "evidence": ["Thoughts that support this belief"],
      "origin": "Likely source of this belief",
      "impact": "How this belief affects daily life"
    }
  ],
  "thoughtPatterns": [
    {
      "pattern": "Description of recurring pattern",
      "frequency": "How often this appears",
      "triggers": ["What typically triggers this pattern"],
      "evolution": "How this pattern has changed over time"
    }
  ],
  "progressIndicators": [
    "Signs of positive change in thinking",
    "Areas where beliefs are becoming more balanced"
  ],
  "recommendedFocus": "Which beliefs to work on next",
  "strengths": ["Positive beliefs and thinking patterns identified"]
}

Be encouraging while being clinically accurate.`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultBeliefAnalysis()
      }
    } catch (error) {
      console.error('Belief analysis generation error:', error)
      return this.getDefaultBeliefAnalysis()
    }
  }

  // ===================================================================
  // 🧠 THERAPEUTIC JOURNALING METHODS
  // ===================================================================

  // Start therapeutic journaling session with emotional check-in
  async startTherapeuticSession(emotionalState = 'neutral', context = {}) {
    try {
      if (!this.apiKey) {
        return this.getDefaultTherapeuticSession()
      }

      const prompt = `You are Serenity AI, the most advanced therapeutic journaling companion. The user is arriving with emotional state: ${emotionalState}.

Context: ${JSON.stringify(context)}

Generate a warm, empathetic check-in response that:
1. Acknowledges their current emotional state
2. Creates a safe, welcoming space
3. Offers three adaptive journaling paths based on their state

Respond with JSON:
{
  "welcomeMessage": "🌙 Welcome back. Let's pause together for a moment...",
  "checkInQuestion": "How are you feeling as you enter this space today?",
  "adaptivePaths": [
    {
      "id": "process_experience",
      "title": "I want to untangle something that's on my mind",
      "description": "Process and work through specific experiences or thoughts",
      "icon": "🧠"
    },
    {
      "id": "connect_deeper_self",
      "title": "I want to connect to my deeper self",
      "description": "Explore values, identity, and personal growth",
      "icon": "💫"
    },
    {
      "id": "imagine_future",
      "title": "I want to imagine a better version of my future",
      "description": "Vision journaling and intentional future design",
      "icon": "🌟"
    }
  ],
  "freeWriteOption": "You can also just free write—I'm here with you either way.",
  "sessionId": "${Date.now()}"
}`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultTherapeuticSession()
      }
    } catch (error) {
      console.error('Therapeutic session start error:', error)
      return this.getDefaultTherapeuticSession()
    }
  }

  // Get adaptive journaling paths based on emotional state
  async getAdaptiveJournalingPaths(emotionalState, previousEntries = []) {
    try {
      if (!this.apiKey) {
        return this.getDefaultJournalingPaths()
      }

      const recentEntries = previousEntries.slice(-3).map(entry =>
        `${entry.mood}: ${entry.content.substring(0, 100)}...`
      ).join('\n')

      const prompt = `Based on emotional state "${emotionalState}" and recent journal patterns, suggest 3 personalized journaling paths:

Recent entries:
${recentEntries || 'No recent entries'}

Respond with JSON array of paths:
[
  {
    "id": "path_id",
    "title": "Path title",
    "description": "What this path offers",
    "techniques": ["technique1", "technique2"],
    "expectedOutcome": "What they'll gain",
    "icon": "emoji"
  }
]`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultJournalingPaths()
      }
    } catch (error) {
      console.error('Adaptive paths error:', error)
      return this.getDefaultJournalingPaths()
    }
  }

  // Generate therapeutic prompts for specific journaling path
  async getTherapeuticPrompts(pathType, emotionalState, userResponse = '') {
    try {
      if (!this.apiKey) {
        return this.getDefaultTherapeuticPrompts(pathType)
      }

      let prompt = `You are Serenity AI providing therapeutic journaling guidance.
Path: ${pathType}
Emotional state: ${emotionalState}
${userResponse ? `User's previous response: "${userResponse}"` : ''}

Generate therapeutic prompts using evidence-based techniques:`

      switch (pathType) {
        case 'process_experience':
          prompt += `
Focus on emotion processing and cognitive reframing. Use techniques like:
- Emotion naming & expansion
- Perspective shifting
- Shadow integration
- Self-compassion

Respond with JSON:
{
  "primaryPrompt": "What's underneath that feeling?",
  "followUpQuestions": ["What would your wiser self say?", "What part of you needs attention?"],
  "technique": "emotion_expansion",
  "guidanceText": "Let's explore what's beneath the surface..."
}`
          break

        case 'connect_deeper_self':
          prompt += `
Focus on values alignment and self-authorship. Use techniques like:
- Values exploration
- Identity reflection
- Meaning-making
- Authentic self discovery

Respond with JSON:
{
  "primaryPrompt": "What does this tell you about what matters most to you?",
  "followUpQuestions": ["Who are you becoming?", "What values are calling to you?"],
  "technique": "values_alignment",
  "guidanceText": "Let's connect with your authentic self..."
}`
          break

        case 'imagine_future':
          prompt += `
Focus on future pacing and narrative building. Use techniques like:
- Vision journaling
- Intentional future design
- Goal alignment
- Possibility exploration

Respond with JSON:
{
  "primaryPrompt": "Describe a day in your ideal future—be vivid and specific.",
  "followUpQuestions": ["What steps lead to this future?", "How does this future feel in your body?"],
  "technique": "future_pacing",
  "guidanceText": "Let's design your intentional future..."
}`
          break

        default:
          prompt += `
Provide general reflective prompts for open journaling.

Respond with JSON:
{
  "primaryPrompt": "What's alive in you right now?",
  "followUpQuestions": ["What wants to be expressed?", "What are you noticing?"],
  "technique": "open_reflection",
  "guidanceText": "Let your thoughts flow freely..."
}`
      }

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultTherapeuticPrompts(pathType)
      }
    } catch (error) {
      console.error('Therapeutic prompts error:', error)
      return this.getDefaultTherapeuticPrompts(pathType)
    }
  }

  // Generate smart goal suggestions
  async generateGoalSuggestions(currentGoals = [], mood = 'neutral', timeframe = 'daily') {
    try {
      if (!this.apiKey) {
        return this.getDefaultGoalSuggestions(timeframe)
      }

      const prompt = `Suggest 3-5 achievable ${timeframe} goals for someone feeling ${mood}.
Current goals: ${currentGoals.join(', ') || 'None specified'}

Respond with a JSON array of goal objects:
[
  {
    "title": "Goal title",
    "description": "Brief description",
    "category": "wellness|productivity|self-care|growth",
    "difficulty": "easy|medium|challenging",
    "timeEstimate": "estimated time",
    "benefits": "Why this goal helps"
  }
]

Make goals SMART (specific, measurable, achievable, relevant, time-bound) and emotionally supportive.`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        const goals = JSON.parse(text)
        return Array.isArray(goals) ? goals : this.getDefaultGoalSuggestions(timeframe)
      } catch {
        return this.getDefaultGoalSuggestions(timeframe)
      }
    } catch (error) {
      console.error('Goal suggestions generation error:', error)
      return this.getDefaultGoalSuggestions(timeframe)
    }
  }

  // Generate focus session guidance
  async generateFocusSession(duration = 25, taskType = 'general', currentMood = 'neutral') {
    try {
      if (!this.apiKey) {
        return this.getDefaultFocusSession(duration)
      }

      const prompt = `Create a ${duration}-minute focus session for ${taskType} work. User is feeling ${currentMood}.

Respond with a JSON object:
{
  "sessionName": "Session title",
  "preparation": "Pre-session preparation steps",
  "focusPrompt": "Motivational focus prompt",
  "breakReminders": ["Reminder 1", "Reminder 2"],
  "reflectionQuestions": ["What went well?", "What was challenging?"],
  "nextSteps": "Suggestions for after the session",
  "moodBooster": "Encouraging message based on current mood"
}

Make it motivational and supportive.`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultFocusSession(duration)
      }
    } catch (error) {
      console.error('Focus session generation error:', error)
      return this.getDefaultFocusSession(duration)
    }
  }

  // Default fallback methods for wellness tools
  getDefaultBreathingExercise(emotion, timeAvailable) {
    const exercises = {
      anxious: {
        name: "Calming 4-7-8 Breathing",
        technique: "4-7-8 breathing",
        instructions: "Inhale through your nose for 4 counts, hold for 7 counts, exhale through your mouth for 8 counts",
        duration: `${timeAvailable} minutes`,
        breathPattern: { inhale: 4, hold: 7, exhale: 8, pause: 0 },
        cycles: Math.max(4, Math.floor(timeAvailable * 60 / 19)),
        guidance: "This technique helps activate your parasympathetic nervous system, promoting calm and relaxation."
      },
      stressed: {
        name: "Box Breathing",
        technique: "box breathing",
        instructions: "Inhale for 4, hold for 4, exhale for 4, hold for 4",
        duration: `${timeAvailable} minutes`,
        breathPattern: { inhale: 4, hold: 4, exhale: 4, pause: 4 },
        cycles: Math.max(5, Math.floor(timeAvailable * 60 / 16)),
        guidance: "Box breathing helps regulate your nervous system and improve focus."
      },
      default: {
        name: "Mindful Breathing",
        technique: "natural breathing",
        instructions: "Breathe naturally while focusing on the sensation of breath entering and leaving your body",
        duration: `${timeAvailable} minutes`,
        breathPattern: { inhale: 4, hold: 2, exhale: 6, pause: 0 },
        cycles: Math.max(6, Math.floor(timeAvailable * 60 / 12)),
        guidance: "Simple mindful breathing helps center your mind and reduce stress."
      }
    }
    return exercises[emotion] || exercises.default
  }

  // Analyze gratitude response for insights
  async analyzeGratitudeResponse(responseText, promptText, emotion = 'neutral') {
    try {
      if (!this.apiKey) {
        return this.getDefaultResponseAnalysis()
      }

      const analysisPrompt = `You are Serenity AI, analyzing a gratitude reflection response for therapeutic insights.

Prompt: "${promptText}"
Response: "${responseText}"
User's emotion: ${emotion}

Analyze this response and provide insights in JSON format:
{
  "emotionalTone": "grateful|hopeful|sad|mixed",
  "sentimentScore": 0.8,
  "wordCount": 45,
  "valuesIdentified": ["connection", "growth", "resilience"],
  "insights": [
    {
      "type": "pattern",
      "text": "Shows strong appreciation for relationships",
      "confidence": 0.9
    }
  ],
  "therapeuticObservations": "User demonstrates healthy coping mechanisms and social support awareness"
}`

      const result = await this.model.generateContent(analysisPrompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultResponseAnalysis()
      }
    } catch (error) {
      console.error('Gratitude response analysis error:', error)
      return this.getDefaultResponseAnalysis()
    }
  }

  // Generate AI companion response for gratitude reflection
  async generateGratitudeCompanionResponse(userResponse, promptText, sessionContext = {}) {
    try {
      if (!this.apiKey) {
        return this.getDefaultCompanionResponse()
      }

      const companionPrompt = `You are Serenity AI, a warm and compassionate therapeutic companion. The user just completed a gratitude reflection.

Prompt they responded to: "${promptText}"
Their response: "${userResponse}"
Session context: ${JSON.stringify(sessionContext)}

Generate a brief, warm response that:
- Acknowledges their reflection with genuine appreciation
- Offers a gentle follow-up question or insight
- Maintains therapeutic boundaries
- Feels personal and encouraging

Keep it under 50 words. Be warm, not clinical.

Example: "I can feel the warmth in your words about your friend's support. That connection sounds really meaningful to you. How does it feel to recognize that kind of love in your life? 💙"`

      const result = await this.model.generateContent(companionPrompt)
      const response = await result.response
      const text = response.text().trim()

      return {
        message: text,
        requiresResponse: true,
        type: 'reflection'
      }
    } catch (error) {
      console.error('Gratitude companion response error:', error)
      return this.getDefaultCompanionResponse()
    }
  }

  // Generate session insights and patterns
  async generateGratitudeSessionInsights(responses, sessionData) {
    try {
      if (!this.apiKey) {
        return this.getDefaultSessionInsights()
      }

      const insightsPrompt = `You are Serenity AI, analyzing a complete gratitude reflection session for therapeutic insights.

Session data:
- Emotion: ${sessionData.emotion}
- Responses: ${JSON.stringify(responses)}
- Duration: ${sessionData.duration} minutes

Generate insights in JSON format:
{
  "overallTheme": "Connection and growth",
  "patterns": [
    {
      "type": "value",
      "pattern": "Strong focus on relationships",
      "evidence": ["mentions family", "appreciates friends"],
      "insight": "User finds meaning through connections"
    }
  ],
  "growth": "Shows increasing self-awareness",
  "recommendations": [
    "Continue exploring relationship gratitude",
    "Consider writing thank you notes"
  ],
  "moodShift": "positive"
}`

      const result = await this.model.generateContent(insightsPrompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultSessionInsights()
      }
    } catch (error) {
      console.error('Gratitude session insights error:', error)
      return this.getDefaultSessionInsights()
    }
  }

  getDefaultGratitudePrompts(emotion) {
    const prompts = {
      sad: [
        { text: "What small act of kindness did someone show you recently?", type: "connection", focus: "support" },
        { text: "What comfort brought you peace today, even briefly?", type: "self-care", focus: "healing" },
        { text: "Who in your life accepts you just as you are?", type: "connection", focus: "acceptance" }
      ],
      anxious: [
        { text: "What made you feel safe or secure today?", type: "grounding", focus: "safety" },
        { text: "What challenge did you handle better than expected?", type: "growth", focus: "resilience" },
        { text: "What support system do you have in your life?", type: "connection", focus: "support" }
      ],
      angry: [
        { text: "What helped you feel more at peace today?", type: "regulation", focus: "calm" },
        { text: "Who showed you patience when you needed it?", type: "connection", focus: "compassion" },
        { text: "What strength did you discover in yourself recently?", type: "growth", focus: "empowerment" }
      ],
      default: [
        { text: "What made you smile today?", type: "appreciation", focus: "joy" },
        { text: "Who are you grateful for and why?", type: "connection", focus: "relationships" },
        { text: "What's something beautiful you noticed today?", type: "mindfulness", focus: "presence" }
      ]
    }
    return prompts[emotion] || prompts.default
  }

  getDefaultResponseAnalysis() {
    return {
      emotionalTone: 'neutral',
      sentimentScore: 0.5,
      wordCount: 0,
      valuesIdentified: [],
      insights: [],
      therapeuticObservations: 'Response analysis unavailable'
    }
  }

  getDefaultCompanionResponse() {
    return {
      message: "Thank you for sharing that reflection with me. Your gratitude is beautiful. 🌟",
      requiresResponse: false,
      type: 'encouragement'
    }
  }

  getDefaultSessionInsights() {
    return {
      overallTheme: 'Gratitude practice',
      patterns: [],
      growth: 'Engaging in reflection',
      recommendations: ['Continue daily gratitude practice'],
      moodShift: 'stable'
    }
  }

  getDefaultDistortionDetection() {
    return {
      primaryDistortions: [
        {
          type: "all-or-nothing",
          confidence: 0.7,
          explanation: "This thought shows black-and-white thinking patterns",
          examples: ["Words like 'always', 'never', 'completely'"]
        }
      ],
      emotionalIntensity: 6,
      thoughtPattern: "Negative self-evaluation with absolute thinking",
      triggerContext: "Likely triggered by a challenging situation or setback",
      coreBeliefs: ["I must be perfect", "Mistakes mean I'm a failure"],
      urgencyLevel: "medium"
    }
  }

  getDefaultEvidenceQuestions() {
    return {
      evidenceFor: [
        "What specific evidence supports this thought?",
        "When has this been true in the past?"
      ],
      evidenceAgainst: [
        "What evidence contradicts this thought?",
        "What examples challenge this perspective?"
      ],
      alternativePerspectives: [
        "How might someone else view this situation?",
        "What would you tell a friend in this situation?"
      ],
      balancedQuestions: [
        "What's a more balanced way to see this?",
        "What nuances are you missing?"
      ],
      futureOriented: [
        "How will this matter in 5 years?",
        "What's the range of possible outcomes?"
      ],
      selfCompassion: [
        "How can you be kinder to yourself about this?",
        "What would self-acceptance look like here?"
      ]
    }
  }

  getDefaultAdvancedReframe() {
    return {
      primaryReframe: {
        technique: "perspective-taking",
        reframedThought: "This situation is challenging, but it doesn't define my worth or capabilities.",
        explanation: "This reframe acknowledges difficulty while maintaining self-compassion",
        believabilityRating: "7"
      },
      alternativeReframes: [
        {
          technique: "time-perspective",
          reframedThought: "This feeling is temporary and will pass with time and self-care.",
          explanation: "Reminds you that difficult emotions are not permanent"
        }
      ],
      selfCompassionScript: "It's okay to struggle sometimes. You're human, and you're doing your best with the resources you have right now.",
      actionableSteps: [
        "Practice the new thought when the old one arises",
        "Write down evidence that supports the balanced perspective",
        "Use breathing exercises when feeling overwhelmed"
      ],
      affirmations: [
        "I am worthy of compassion, especially from myself",
        "I can handle challenges with grace and wisdom"
      ],
      copingStatements: [
        "This thought is not a fact",
        "I choose to see this situation with balance and kindness"
      ],
      progressMarkers: [
        "Noticing the old thought pattern more quickly",
        "Feeling less emotional intensity when the thought arises",
        "Naturally thinking more balanced thoughts"
      ]
    }
  }

  getDefaultInnerCriticDialogue() {
    return {
      innerCritic: {
        voice: "You're not good enough and you never will be.",
        tactics: ["perfectionism", "fear-mongering"],
        underlyingFear: "Fear of rejection and failure"
      },
      innerAlly: {
        voice: "I understand you're trying to protect me, but this harsh approach isn't helping.",
        approach: "Acknowledges the critic's intention while setting boundaries",
        reframe: "I am enough as I am, and I can grow with kindness and patience."
      },
      dialogue: [
        {
          speaker: "critic",
          message: "You're going to fail and everyone will see how inadequate you are.",
          tone: "harsh"
        },
        {
          speaker: "ally",
          message: "I hear your concern about failure. Let's look at this more realistically.",
          tone: "gentle"
        },
        {
          speaker: "ally",
          message: "I've handled challenges before, and I can handle this one too.",
          tone: "firm"
        }
      ],
      resolution: "The inner ally acknowledges the critic's protective intention while choosing self-compassion and realistic thinking.",
      practicePhrase: "Thank you for trying to protect me, but I choose kindness."
    }
  }

  getDefaultBeliefAnalysis() {
    return {
      coreBeliefs: [
        {
          belief: "I must be perfect to be worthy",
          strength: 0.7,
          evidence: ["Self-critical thoughts", "Fear of making mistakes"],
          origin: "Likely developed from early experiences with high expectations",
          impact: "Creates anxiety and prevents taking healthy risks"
        }
      ],
      thoughtPatterns: [
        {
          pattern: "Self-critical evaluation after challenges",
          frequency: "Often triggered by setbacks",
          triggers: ["Mistakes", "Criticism", "Comparison with others"],
          evolution: "Becoming more aware of this pattern is the first step to change"
        }
      ],
      progressIndicators: [
        "Increased awareness of thought patterns",
        "Willingness to explore and challenge thoughts"
      ],
      recommendedFocus: "Developing self-compassion and realistic thinking",
      strengths: [
        "Shows insight and willingness to grow",
        "Demonstrates courage in examining difficult thoughts"
      ]
    }
  }

  getDefaultCognitiveReframe() {
    return {
      analysis: "It's natural to have challenging thoughts. Let's explore this together.",
      questions: [
        "What evidence supports this thought?",
        "What evidence contradicts this thought?",
        "What would you tell a friend having this thought?",
        "What's a more balanced way to view this situation?"
      ],
      reframe: "Consider that thoughts are not facts. This situation may have multiple perspectives worth exploring.",
      affirmation: "I am capable of handling challenges and viewing situations with clarity and compassion.",
      technique: "Thought challenging and perspective taking"
    }
  }

  getDefaultGoalSuggestions(timeframe) {
    const goals = {
      daily: [
        {
          title: "Mindful Morning",
          description: "Start the day with 5 minutes of mindful breathing",
          category: "wellness",
          difficulty: "easy",
          timeEstimate: "5 minutes",
          benefits: "Sets a calm, intentional tone for the day"
        },
        {
          title: "Gratitude Note",
          description: "Write down three things you're grateful for",
          category: "self-care",
          difficulty: "easy",
          timeEstimate: "3 minutes",
          benefits: "Shifts focus to positive aspects of life"
        },
        {
          title: "Movement Break",
          description: "Take a 10-minute walk or stretch",
          category: "wellness",
          difficulty: "easy",
          timeEstimate: "10 minutes",
          benefits: "Boosts mood and energy levels"
        }
      ],
      weekly: [
        {
          title: "Digital Detox Hour",
          description: "Spend one hour without screens each day",
          category: "self-care",
          difficulty: "medium",
          timeEstimate: "7 hours total",
          benefits: "Reduces stress and improves mental clarity"
        },
        {
          title: "Creative Expression",
          description: "Engage in a creative activity you enjoy",
          category: "growth",
          difficulty: "easy",
          timeEstimate: "2-3 hours",
          benefits: "Enhances mood and self-expression"
        }
      ]
    }
    return goals[timeframe] || goals.daily
  }

  // Process therapeutic response with AI guidance
  async processTherapeuticResponse(response, promptType, sessionContext = {}) {
    try {
      if (!this.apiKey) {
        return this.getDefaultTherapeuticProcessing()
      }

      const prompt = `You are Serenity AI processing a therapeutic journaling response.

User's response: "${response}"
Prompt type: ${promptType}
Session context: ${JSON.stringify(sessionContext)}

Analyze the response and provide therapeutic guidance using mirror phrases and personalized insights:

Respond with JSON:
{
  "mirrorPhrase": "I hear that you're feeling...",
  "insight": "What I notice in your words is...",
  "followUpPrompt": "What would it be like if...",
  "therapeuticObservation": "Your response shows...",
  "encouragement": "There's wisdom in...",
  "nextStep": "You might explore..."
}`

      const result = await this.model.generateContent(prompt)
      const response_text = await result.response
      const text = response_text.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultTherapeuticProcessing()
      }
    } catch (error) {
      console.error('Therapeutic processing error:', error)
      return this.getDefaultTherapeuticProcessing()
    }
  }

  // Generate closing reflection for therapeutic session
  async generateClosingReflection(sessionData, entryContent) {
    try {
      if (!this.apiKey) {
        return this.getDefaultClosingReflection()
      }

      const prompt = `Generate a closing reflection for this therapeutic journaling session:

Session data: ${JSON.stringify(sessionData)}
Entry content: "${entryContent}"

Create a meaningful closing that includes:
1. Acknowledgment of their work
2. A reflection prompt
3. Integration question

Respond with JSON:
{
  "acknowledgment": "Thank you for bringing such honesty to this space...",
  "reflectionPrompts": [
    "What truth emerged for you today?",
    "What's one thing you want to remember from this entry?",
    "How might you carry this into tomorrow?"
  ],
  "integrationQuestion": "As you close this session, what feels most important to hold onto?",
  "encouragement": "Your willingness to explore and reflect is a gift to yourself."
}`

      const result = await this.model.generateContent(prompt)
      const response = await result.response
      const text = response.text().trim()

      try {
        return JSON.parse(text)
      } catch {
        return this.getDefaultClosingReflection()
      }
    } catch (error) {
      console.error('Closing reflection error:', error)
      return this.getDefaultClosingReflection()
    }
  }

  // Default fallback methods for therapeutic journaling
  getDefaultTherapeuticSession() {
    return {
      welcomeMessage: "🌙 Welcome back. Let's pause together for a moment.",
      checkInQuestion: "How are you feeling as you enter this space today?",
      adaptivePaths: [
        {
          id: "process_experience",
          title: "I want to untangle something that's on my mind",
          description: "Process and work through specific experiences or thoughts",
          icon: "🧠"
        },
        {
          id: "connect_deeper_self",
          title: "I want to connect to my deeper self",
          description: "Explore values, identity, and personal growth",
          icon: "💫"
        },
        {
          id: "imagine_future",
          title: "I want to imagine a better version of my future",
          description: "Vision journaling and intentional future design",
          icon: "🌟"
        }
      ],
      freeWriteOption: "You can also just free write—I'm here with you either way.",
      sessionId: Date.now().toString()
    }
  }

  getDefaultJournalingPaths() {
    return [
      {
        id: "emotional_processing",
        title: "Process emotions and experiences",
        description: "Work through feelings and recent experiences",
        techniques: ["emotion naming", "perspective shifting"],
        expectedOutcome: "Greater emotional clarity and understanding",
        icon: "💙"
      },
      {
        id: "self_discovery",
        title: "Explore identity and values",
        description: "Connect with your authentic self and core values",
        techniques: ["values exploration", "identity reflection"],
        expectedOutcome: "Deeper self-awareness and alignment",
        icon: "✨"
      },
      {
        id: "future_visioning",
        title: "Design your intentional future",
        description: "Create vivid visions of your desired future",
        techniques: ["future pacing", "goal alignment"],
        expectedOutcome: "Clear direction and motivation",
        icon: "🌟"
      }
    ]
  }

  getDefaultTherapeuticPrompts(pathType) {
    const prompts = {
      process_experience: {
        primaryPrompt: "What's on your mind that you'd like to explore?",
        followUpQuestions: ["What emotions are present?", "What would help right now?"],
        technique: "open_exploration",
        guidanceText: "Let's gently explore what's present for you..."
      },
      connect_deeper_self: {
        primaryPrompt: "What matters most to you right now?",
        followUpQuestions: ["What values are calling to you?", "Who are you becoming?"],
        technique: "values_exploration",
        guidanceText: "Let's connect with your authentic self..."
      },
      imagine_future: {
        primaryPrompt: "Describe a day in your ideal future.",
        followUpQuestions: ["How does this future feel?", "What steps lead there?"],
        technique: "future_visioning",
        guidanceText: "Let's design your intentional future..."
      }
    }
    return prompts[pathType] || prompts.process_experience
  }

  getDefaultTherapeuticProcessing() {
    return {
      mirrorPhrase: "I hear the depth in what you're sharing.",
      insight: "Your willingness to reflect shows great self-awareness.",
      followUpPrompt: "What feels most important about this for you?",
      therapeuticObservation: "There's wisdom in your reflection.",
      encouragement: "Thank you for bringing such honesty to this space.",
      nextStep: "Continue to trust your inner knowing."
    }
  }

  getDefaultClosingReflection() {
    return {
      acknowledgment: "Thank you for taking this time to reflect and explore.",
      reflectionPrompts: [
        "What truth emerged for you today?",
        "What's one thing you want to remember from this entry?",
        "How might you carry this into tomorrow?"
      ],
      integrationQuestion: "As you close this session, what feels most important to hold onto?",
      encouragement: "Your commitment to self-reflection is a beautiful gift to yourself."
    }
  }

  getDefaultFocusSession(duration) {
    return {
      sessionName: `${duration}-Minute Focus Session`,
      preparation: "Clear your workspace, silence notifications, and set a clear intention for this session.",
      focusPrompt: "You have the ability to focus deeply. Trust in your capacity to accomplish meaningful work.",
      breakReminders: [
        "Remember to breathe deeply and maintain good posture",
        "If your mind wanders, gently bring it back to your task"
      ],
      reflectionQuestions: [
        "What went well during this session?",
        "What was challenging and how did you handle it?",
        "What would you do differently next time?"
      ],
      nextSteps: "Take a 5-minute break to stretch and hydrate before your next task.",
      moodBooster: "You've completed a focused work session. That's an accomplishment worth celebrating!"
    }
  }

  getFallbackResponse(userMessage) {
    const fallbackResponses = [
      "I'm here to listen and support you. While I'm experiencing some technical difficulties right now, I want you to know that your feelings are valid and important. Can you tell me more about what's on your mind?",
      "Thank you for sharing with me. I'm having some connection issues at the moment, but I'm still here for you. What would be most helpful for you to talk about right now?",
      "I appreciate you reaching out. Even though I'm having some technical challenges, I want you to know that you're not alone. What's been weighing on your heart lately?",
      "I'm experiencing some technical difficulties, but I want to acknowledge what you've shared. Your thoughts and feelings matter. How can I best support you today?",
      "While I'm having some connection issues right now, I want you to know that I'm here to listen. What feels most important for you to express today?"
    ]

    return fallbackResponses[Math.floor(Math.random() * fallbackResponses.length)]
  }
}

export default new GeminiService()

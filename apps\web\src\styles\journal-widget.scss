// ===================================================================
// 📖 ADVANCED JOURNAL WIDGET - ULTRA-PREMIUM STYLING
// ===================================================================
// Professional therapeutic design for journaling with AI integration
// Surpassing macOS design quality with advanced aesthetics
// ===================================================================

@import './variables.scss';

// ===================================================================
// 🎨 JOURNAL-SPECIFIC COLOR PALETTE
// ===================================================================

:root {
  // Journal Primary Colors
  --journal-primary: #7A9B8E; // Sage Green - Calming, therapeutic
  --journal-primary-dark: #5A8B7A; // Darker sage - Depth
  --journal-primary-light: #A8C5B8; // Light sage - Subtle accents

  // Journal Secondary Colors
  --journal-accent: #A8C5E2; // Soft Blue - Trust, clarity
  --journal-warm: #F4A261; // Warm Peach - Encouragement
  --journal-lavender: #C8B5D1; // Muted Lavender - Compassion

  // Journal Glass Effects
  --journal-glass-bg: rgba(248, 250, 252, 0.85);
  --journal-glass-border: rgba(255, 255, 255, 0.3);
  --journal-backdrop-blur: blur(20px);

  // Journal Shadows
  --journal-shadow-soft: 0 4px 20px rgba(122, 155, 142, 0.15);
  --journal-shadow-medium: 0 8px 32px rgba(122, 155, 142, 0.2);
  --journal-shadow-strong: 0 16px 48px rgba(122, 155, 142, 0.25);

  // Journal Gradients
  --journal-gradient-primary: linear-gradient(135deg, #7A9B8E 0%, #A8C5B8 100%);
  --journal-gradient-accent: linear-gradient(135deg, #A8C5E2 0%, #C8B5D1 100%);
  --journal-gradient-warm: linear-gradient(135deg, #F4A261 0%, #E76F51 100%);
}

// ===================================================================
// 🌟 GLASSMORPHISM BASE CLASSES
// ===================================================================

.journal-glass {
  background: var(--journal-glass-bg);
  backdrop-filter: var(--journal-backdrop-blur);
  border: 1px solid var(--journal-glass-border);
  box-shadow: var(--journal-shadow-soft);

  &--elevated {
    box-shadow: var(--journal-shadow-medium);
  }

  &--floating {
    box-shadow: var(--journal-shadow-strong);
  }
}

.journal-gradient-bg {
  background: linear-gradient(135deg,
      var(--journal-glass-bg),
      rgba(248, 250, 252, 0.95));
}

// ===================================================================
// 📖 MAIN JOURNAL WIDGET STYLING
// ===================================================================

.journal-widget {
  position: relative;
  overflow: hidden;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--journal-glass-border);

    .widget__title {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--journal-primary-dark);

      .widget__icon {
        color: var(--journal-primary);
      }
    }

    .widget__action {
      @extend .journal-glass;
      padding: var(--spacing-sm) var(--spacing-md);
      border: none;
      border-radius: var(--border-radius-lg);
      background: var(--journal-gradient-primary);
      color: var(--white);
      font-weight: var(--font-weight-medium);
      font-size: var(--font-size-sm);
      cursor: pointer;
      transition: all var(--transition-smooth);
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--journal-shadow-medium);
        background: var(--journal-gradient-accent);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  // ===================================================================
  // 📝 JOURNAL ENTRIES STYLING
  // ===================================================================

  &__entries {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
    max-height: 400px;
    overflow-y: auto;
    padding-right: var(--spacing-xs);

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--journal-glass-bg);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--journal-primary);
      border-radius: 3px;

      &:hover {
        background: var(--journal-primary-dark);
      }
    }
  }

  &__entry {
    @extend .journal-glass;
    @extend .journal-glass--elevated;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-xl);
    border-left: 4px solid var(--journal-primary);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all var(--transition-smooth);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--journal-gradient-primary);
      opacity: 0;
      transition: opacity var(--transition-smooth);
      z-index: -1;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--journal-shadow-strong);
      border-left-color: var(--journal-accent);

      &::before {
        opacity: 0.05;
      }
    }

    &--ai-analyzed {
      border-left-color: var(--journal-accent);

      &::after {
        content: '✨';
        position: absolute;
        top: var(--spacing-sm);
        right: var(--spacing-sm);
        font-size: var(--font-size-lg);
        opacity: 0.7;
      }
    }
  }

  &__entry-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);

    .entry-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }

    .entry-stats {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      font-size: var(--font-size-xs);
      color: var(--gray-500);
    }
  }

  &__entry-date {
    font-size: var(--font-size-sm);
    color: var(--journal-primary-dark);
    font-weight: var(--font-weight-medium);
  }

  &__entry-mood {
    font-size: var(--font-size-lg);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }

  &__entry-preview {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  &__entry-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
  }

  &__entry-action {
    @extend .journal-glass;
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--journal-glass-border);
    border-radius: var(--border-radius-md);
    background: transparent;
    color: var(--journal-primary-dark);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-smooth);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);

    &:hover {
      background: var(--journal-primary);
      color: var(--white);
      transform: translateY(-1px);
      box-shadow: var(--journal-shadow-soft);
    }

    &--ai {
      border-color: var(--journal-accent);
      color: var(--journal-accent);

      &:hover {
        background: var(--journal-accent);
        color: var(--white);
      }
    }

    &--warm {
      border-color: var(--journal-warm);
      color: var(--journal-warm);

      &:hover {
        background: var(--journal-warm);
        color: var(--white);
      }
    }
  }

  // ===================================================================
  // ✨ NEW ENTRY BUTTON STYLING
  // ===================================================================

  &__new-entry {
    @extend .journal-glass;
    @extend .journal-glass--elevated;
    width: 100%;
    padding: var(--spacing-lg);
    border: 2px dashed var(--journal-primary);
    border-radius: var(--border-radius-xl);
    background: var(--journal-gradient-bg);
    color: var(--journal-primary-dark);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--journal-gradient-primary);
      opacity: 0;
      transition: opacity var(--transition-smooth);
      z-index: -1;
    }

    &:hover {
      border-color: var(--journal-accent);
      color: var(--journal-accent);
      transform: translateY(-2px);
      box-shadow: var(--journal-shadow-medium);

      &::before {
        opacity: 0.1;
      }
    }

    &:active {
      transform: translateY(0);
    }
  }
}

// ===================================================================
// 🎯 LOADING AND ERROR STATES
// ===================================================================

.journal-widget {
  &__loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;

    &-spinner {
      color: var(--journal-primary);
      margin-bottom: var(--spacing-md);
    }

    p {
      color: var(--gray-600);
      font-size: var(--font-size-sm);
    }
  }

  &__error {
    @extend .journal-glass;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    border-left: 4px solid var(--journal-warm);
    text-align: center;

    p {
      color: var(--gray-700);
      margin-bottom: var(--spacing-md);
    }
  }

  &__retry-btn {
    @extend .journal-glass;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--journal-warm);
    border-radius: var(--border-radius-md);
    background: transparent;
    color: var(--journal-warm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-smooth);

    &:hover {
      background: var(--journal-warm);
      color: var(--white);
    }
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;

    &-icon {
      color: var(--journal-primary);
      margin-bottom: var(--spacing-lg);
      opacity: 0.7;
    }

    h4 {
      color: var(--journal-primary-dark);
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      margin-bottom: var(--spacing-sm);
    }

    p {
      color: var(--gray-600);
      font-size: var(--font-size-sm);
      max-width: 300px;
    }
  }
}

// ===================================================================
// 📝 ENTRY TITLE STYLING
// ===================================================================

.journal-widget {
  &__entry-title {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    color: var(--journal-primary-dark);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
  }
}


// ===================================================================
// 🎭 MODAL SYSTEM STYLING
// ===================================================================

.journal-modal {
  &__overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    padding: var(--spacing-lg);
  }

  &__content {
    @extend .journal-glass;
    @extend .journal-glass--floating;
    background: var(--white);
    border-radius: var(--border-radius-2xl);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    &--large {
      max-width: 800px;
    }
  }

  &__header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--journal-glass-border);
    display: flex;
    align-items: center;
    justify-content: space-between;

    h2 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--journal-primary-dark);
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }
  }

  &__close {
    @extend .journal-glass;
    padding: var(--spacing-sm);
    border: 1px solid var(--journal-glass-border);
    border-radius: var(--border-radius-md);
    background: transparent;
    color: var(--gray-500);
    cursor: pointer;
    transition: all var(--transition-smooth);

    &:hover {
      background: var(--journal-primary);
      color: var(--white);
      transform: scale(1.05);
    }
  }

  &__body {
    padding: var(--spacing-xl);
    overflow-y: auto;
    flex: 1;
  }

  &__footer {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--journal-glass-border);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
  }
}

// ===================================================================
// 📝 JOURNAL FORM STYLING
// ===================================================================

.journal-form {
  &__field {
    margin-bottom: var(--spacing-lg);

    label {
      display: block;
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--journal-primary-dark);
      margin-bottom: var(--spacing-sm);
    }
  }

  &__input {
    @extend .journal-glass;
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--journal-glass-border);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    transition: all var(--transition-smooth);

    &:focus {
      outline: none;
      border-color: var(--journal-primary);
      box-shadow: 0 0 0 3px rgba(122, 155, 142, 0.1);
    }

    &::placeholder {
      color: var(--gray-400);
    }
  }

  &__textarea {
    @extend .journal-form__input;
    resize: vertical;
    min-height: 200px;
    font-family: var(--font-primary);
    line-height: 1.6;
  }

  &__content-wrapper {
    position: relative;
  }

  &__voice-btn {
    position: absolute;
    bottom: var(--spacing-md);
    right: var(--spacing-md);
    @extend .journal-glass;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--journal-accent);
    border-radius: var(--border-radius-md);
    background: transparent;
    color: var(--journal-accent);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-smooth);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);

    &:hover {
      background: var(--journal-accent);
      color: var(--white);
    }

    &--recording {
      background: var(--journal-warm);
      border-color: var(--journal-warm);
      color: var(--white);
      animation: pulse 1.5s infinite;
    }
  }

  &__stats {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-xs);
    color: var(--gray-500);
  }

  &__mood-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-sm);
  }

  &__mood-option {
    @extend .journal-glass;
    padding: var(--spacing-md);
    border: 1px solid var(--journal-glass-border);
    border-radius: var(--border-radius-lg);
    background: transparent;
    cursor: pointer;
    transition: all var(--transition-smooth);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);

    .mood-emoji {
      font-size: var(--font-size-xl);
    }

    .mood-label {
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      color: var(--gray-600);
    }

    &:hover {
      border-color: var(--journal-primary);
      transform: translateY(-2px);
      box-shadow: var(--journal-shadow-soft);
    }

    &--selected {
      border-color: var(--journal-primary);
      background: var(--journal-gradient-primary);
      color: var(--white);

      .mood-label {
        color: var(--white);
      }
    }
  }
}

// ===================================================================
// 🔘 BUTTON STYLING
// ===================================================================

.journal-btn {
  @extend .journal-glass;
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid var(--journal-glass-border);
  border-radius: var(--border-radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-smooth);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &--primary {
    background: var(--journal-gradient-primary);
    border-color: var(--journal-primary);
    color: var(--white);

    &:hover:not(:disabled) {
      background: var(--journal-gradient-accent);
      transform: translateY(-1px);
      box-shadow: var(--journal-shadow-medium);
    }
  }

  &--secondary {
    background: transparent;
    border-color: var(--journal-glass-border);
    color: var(--journal-primary-dark);

    &:hover:not(:disabled) {
      background: var(--journal-primary);
      color: var(--white);
    }
  }

  &--ai {
    background: transparent;
    border-color: var(--journal-accent);
    color: var(--journal-accent);

    &:hover:not(:disabled) {
      background: var(--journal-accent);
      color: var(--white);
    }
  }
}

// ===================================================================
// 📖 JOURNAL VIEW STYLING
// ===================================================================

.journal-view {
  &__meta {
    flex: 1;
  }

  &__details {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
  }

  &__date {
    font-weight: var(--font-weight-medium);
  }

  &__mood {
    font-size: var(--font-size-lg);
  }

  &__stats {
    color: var(--gray-500);
  }

  &__content {
    font-size: var(--font-size-md);
    line-height: 1.7;
    color: var(--gray-700);
    white-space: pre-wrap;
    margin-bottom: var(--spacing-xl);
  }

  &__ai-section {
    @extend .journal-glass;
    @extend .journal-glass--elevated;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-xl);
    border-left: 4px solid var(--journal-accent);
    margin-top: var(--spacing-xl);

    h3 {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--journal-accent);
      margin-bottom: var(--spacing-md);
    }
  }

  &__ai-content {
    font-size: var(--font-size-sm);
    line-height: 1.6;
    color: var(--gray-700);
  }
}

// ===================================================================
// 🧠 AI INSIGHTS STYLING
// ===================================================================

.journal-ai {
  &__loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;

    div {
      color: var(--journal-accent);
      margin-bottom: var(--spacing-lg);
    }

    p {
      color: var(--gray-600);
      font-size: var(--font-size-sm);
    }
  }

  &__insights {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
  }

  &__section {
    @extend .journal-glass;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-xl);
    border-left: 4px solid var(--journal-primary);

    h3 {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--journal-primary-dark);
      margin-bottom: var(--spacing-md);
    }

    p {
      font-size: var(--font-size-sm);
      line-height: 1.6;
      color: var(--gray-700);
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        font-size: var(--font-size-sm);
        line-height: 1.6;
        color: var(--gray-700);
        padding: var(--spacing-sm) 0;
        border-bottom: 1px solid var(--journal-glass-border);
        position: relative;
        padding-left: var(--spacing-lg);

        &:last-child {
          border-bottom: none;
        }

        &::before {
          content: '•';
          position: absolute;
          left: 0;
          color: var(--journal-primary);
          font-weight: bold;
        }
      }
    }

    &:nth-child(1) {
      border-left-color: var(--journal-warm);

      h3 {
        color: var(--journal-warm);
      }
    }

    &:nth-child(2) {
      border-left-color: var(--journal-accent);

      h3 {
        color: var(--journal-accent);
      }
    }

    &:nth-child(3) {
      border-left-color: var(--journal-lavender);

      h3 {
        color: var(--journal-lavender);
      }
    }

    &:nth-child(4) {
      border-left-color: var(--journal-primary);

      h3 {
        color: var(--journal-primary);
      }
    }
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    text-align: center;

    svg {
      color: var(--journal-accent);
      margin-bottom: var(--spacing-lg);
      opacity: 0.7;
    }

    p {
      color: var(--gray-600);
      font-size: var(--font-size-sm);
      max-width: 300px;
    }
  }
}

// ===================================================================
// 🎬 ANIMATIONS
// ===================================================================

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// ===================================================================
// 📱 RESPONSIVE DESIGN
// ===================================================================

@media (max-width: 768px) {
  .journal-widget {
    &__entry {
      padding: var(--spacing-md);
    }

    &__entry-actions {
      justify-content: center;
    }

    &__new-entry {
      padding: var(--spacing-md);
      font-size: var(--font-size-sm);
    }
  }

  .journal-modal {
    &__overlay {
      padding: var(--spacing-md);
    }

    &__content {
      max-width: 100%;
    }

    &__header,
    &__body,
    &__footer {
      padding: var(--spacing-lg);
    }
  }

  .journal-form {
    &__mood-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}

// ===================================================================
// 🗑️ DELETE CONFIRMATION MODAL
// ===================================================================

.delete-confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  animation: fadeInOverlay 0.3s ease-out forwards;
}

@keyframes fadeInOverlay {
  to {
    opacity: 1;
  }
}

.delete-confirmation-modal {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-2xl);
  max-width: 420px;
  width: 90%;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.15),
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  transform: scale(0.9) translateY(20px);
  animation: slideInModal 0.3s ease-out forwards;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--journal-warm), var(--journal-primary));
    border-radius: var(--border-radius-xl) var(--border-radius-xl) 0 0;
  }
}

@keyframes slideInModal {
  to {
    transform: scale(1) translateY(0);
  }
}

.delete-confirmation-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);

  &__icon {
    width: 64px;
    height: 64px;
    margin: 0 auto var(--spacing-lg);
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 28px;
    box-shadow: 0 8px 24px rgba(255, 107, 107, 0.3);
    animation: pulseIcon 2s ease-in-out infinite;
  }

  &__title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
    line-height: 1.3;
  }

  &__subtitle {
    font-size: var(--font-size-md);
    color: var(--gray-600);
    line-height: 1.5;
  }
}

@keyframes pulseIcon {

  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 8px 24px rgba(255, 107, 107, 0.3);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 32px rgba(255, 107, 107, 0.4);
  }
}

.delete-confirmation-content {
  margin-bottom: var(--spacing-xl);

  &__entry-preview {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);

    &-title {
      font-weight: var(--font-weight-semibold);
      color: var(--gray-800);
      margin-bottom: var(--spacing-xs);
      font-size: var(--font-size-sm);
    }

    &-excerpt {
      color: var(--gray-600);
      font-size: var(--font-size-sm);
      line-height: 1.5;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    &-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-top: var(--spacing-sm);
      font-size: var(--font-size-xs);
      color: var(--gray-500);

      span {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
      }
    }
  }

  &__warning {
    background: linear-gradient(135deg, #fff5f5, #fef2f2);
    border: 1px solid #fecaca;
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);

    &-icon {
      color: #dc2626;
      font-size: var(--font-size-lg);
      margin-top: 2px;
      flex-shrink: 0;
    }

    &-text {
      color: #7f1d1d;
      font-size: var(--font-size-sm);
      line-height: 1.5;
      font-weight: var(--font-weight-medium);
    }
  }
}

.delete-confirmation-actions {
  display: flex;
  gap: var(--spacing-md);

  &__button {
    flex: 1;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-smooth);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }

    &:hover::before {
      left: 100%;
    }

    &--cancel {
      background: var(--white);
      color: var(--gray-700);
      border-color: var(--gray-300);

      &:hover {
        background: var(--gray-50);
        border-color: var(--gray-400);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
      }
    }

    &--delete {
      background: linear-gradient(135deg, #dc2626, #b91c1c);
      color: var(--white);
      border-color: #dc2626;

      &:hover {
        background: linear-gradient(135deg, #b91c1c, #991b1b);
        border-color: #b91c1c;
        transform: translateY(-1px);
        box-shadow: 0 8px 24px rgba(220, 38, 38, 0.3);
      }

      &:active {
        transform: translateY(0);
      }

      &:disabled {
        background: var(--gray-400);
        border-color: var(--gray-400);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;

        &::before {
          display: none;
        }
      }
    }
  }
}

// Loading state for delete button
.delete-confirmation-actions__button--delete.loading {
  position: relative;
  color: transparent;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

// Responsive design
@media (max-width: 480px) {
  .delete-confirmation-modal {
    margin: var(--spacing-lg);
    padding: var(--spacing-xl);
    max-width: none;
    width: auto;
  }

  .delete-confirmation-actions {
    flex-direction: column;

    &__button {
      padding: var(--spacing-lg);
    }
  }

  .delete-confirmation-header__icon {
    width: 56px;
    height: 56px;
    font-size: 24px;
  }
}

// ===================================================================
// 🧠 THERAPEUTIC JOURNALING MODAL STYLING
// ===================================================================

.therapeutic-modal {
  &__overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(12px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: var(--spacing-lg);
  }

  &__content {
    @extend .journal-glass;
    @extend .journal-glass--floating;
    background: var(--white);
    border-radius: var(--border-radius-2xl);
    width: 100%;
    max-width: 700px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  &__header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--journal-glass-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--journal-gradient-primary);
    color: var(--white);

    h2 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      color: var(--white);
    }
  }

  &__close {
    background: transparent;
    border: none;
    color: var(--white);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-md);
    transition: all var(--transition-smooth);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.1);
    }
  }

  &__body {
    padding: var(--spacing-xl);
    overflow-y: auto;
    flex: 1;

    // Custom scrollbar
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--journal-glass-bg);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--journal-primary);
      border-radius: 3px;

      &:hover {
        background: var(--journal-primary-dark);
      }
    }
  }
}

// ===================================================================
// 🌟 THERAPEUTIC CHECK-IN STYLING
// ===================================================================

.therapeutic-checkin {
  &__welcome {
    text-align: center;
    margin-bottom: var(--spacing-xl);

    h3 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--journal-primary-dark);
      margin-bottom: var(--spacing-md);
    }

    p {
      font-size: var(--font-size-md);
      color: var(--gray-600);
      line-height: 1.6;
    }
  }

  &__emotions {
    h4 {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-medium);
      color: var(--journal-primary-dark);
      margin-bottom: var(--spacing-lg);
      text-align: center;
    }
  }
}

.emotion-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.emotion-option {
  @extend .journal-glass;
  @extend .journal-glass--elevated;
  padding: var(--spacing-lg);
  border: 2px solid transparent;
  border-radius: var(--border-radius-xl);
  background: var(--journal-gradient-bg);
  cursor: pointer;
  transition: all var(--transition-smooth);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  text-align: center;

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--journal-shadow-medium);
    border-color: var(--journal-primary);
  }

  &--selected {
    border-color: var(--journal-accent);
    background: var(--journal-gradient-accent);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--journal-shadow-strong);
  }

  .emotion-emoji {
    font-size: 2rem;
    margin-bottom: var(--spacing-xs);
  }

  .emotion-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
  }
}

// ===================================================================
// 🛤️ THERAPEUTIC PATHS STYLING
// ===================================================================

.therapeutic-paths {
  &__intro {
    text-align: center;
    margin-bottom: var(--spacing-xl);

    h3 {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-semibold);
      color: var(--journal-primary-dark);
      margin-bottom: var(--spacing-md);
    }

    p {
      font-size: var(--font-size-md);
      color: var(--gray-600);
      line-height: 1.6;
    }
  }

  &__freewrite {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-xl);
    border-top: 1px solid var(--journal-glass-border);

    p {
      font-size: var(--font-size-sm);
      color: var(--gray-600);
      margin-bottom: var(--spacing-md);
      font-style: italic;
    }
  }
}

.journaling-paths {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.journaling-path {
  @extend .journal-glass;
  @extend .journal-glass--elevated;
  padding: var(--spacing-xl);
  border: 2px solid transparent;
  border-radius: var(--border-radius-xl);
  background: var(--journal-gradient-bg);
  cursor: pointer;
  transition: all var(--transition-smooth);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  text-align: left;

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--journal-shadow-medium);
    border-color: var(--journal-primary);
  }

  .path-icon {
    font-size: 2.5rem;
    flex-shrink: 0;
  }

  .path-content {
    flex: 1;

    h4 {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--journal-primary-dark);
      margin-bottom: var(--spacing-sm);
    }

    p {
      font-size: var(--font-size-sm);
      color: var(--gray-600);
      line-height: 1.5;
    }
  }
}

.freewrite-option {
  @extend .journal-glass;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid var(--journal-primary);
  border-radius: var(--border-radius-lg);
  background: transparent;
  color: var(--journal-primary);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-smooth);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);

  &:hover {
    background: var(--journal-primary);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--journal-shadow-soft);
  }
}

// ===================================================================
// 📱 THERAPEUTIC MODAL RESPONSIVE DESIGN
// ===================================================================

@media (max-width: 768px) {
  .therapeutic-modal {
    &__overlay {
      padding: var(--spacing-md);
    }

    &__content {
      max-height: 95vh;
    }

    &__header {
      padding: var(--spacing-lg);
    }

    &__body {
      padding: var(--spacing-lg);
    }
  }

  .emotion-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: var(--spacing-sm);
  }

  .journaling-path {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);

    .path-icon {
      font-size: 2rem;
    }
  }

  .journal-widget__actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}
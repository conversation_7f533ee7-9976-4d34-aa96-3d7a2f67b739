# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=serenityai
DB_USER=postgres
DB_PASSWORD=Lukinda@1956
DB_SSL=false

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_REFRESH_EXPIRES_IN=30d

# CORS Configuration
CORS_ORIGIN=http://localhost:3001

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
BCRYPT_ROUNDS=12

# Logging
LOG_LEVEL=info

# Google Gemini AI Configuration
GEMINI_API_KEY=AIzaSyAdAJod78n6xaHYSGtZhO8BBVGigsyIwLo

# Voice Transcription Services (Optional - choose one or both)
# OpenAI Whisper API (Recommended for best accuracy)
OPENAI_API_KEY=your_openai_api_key_here

# Google Speech-to-Text API (Alternative option)
# GOOGLE_APPLICATION_CREDENTIALS=path/to/your/google-service-account.json
# GOOGLE_PROJECT_ID=your_google_project_id

# File Upload Configuration
MAX_FILE_SIZE=********
UPLOAD_PATH=uploads

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Mic, Square, Play, Save, Trash2, Volume2, <PERSON>, Heart, PenTool, BookOpen, <PERSON>rkles, X } from 'lucide-react'
import { createPortal } from 'react-dom'
import Card from '../shared/Card'
import { journalAPI } from '../../services/api'

const VoiceJournalWidget = () => {
  // Basic voice recording states
  const [isRecording, setIsRecording] = useState(false)
  const [hasRecording, setHasRecording] = useState(false)
  const [transcription, setTranscription] = useState('')
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  // Therapeutic Voice Journaling states
  const [showTherapeuticModal, setShowTherapeuticModal] = useState(false)
  const [therapeuticSession, setTherapeuticSession] = useState(null)
  const [selectedJournalingPath, setSelectedJournalingPath] = useState(null)
  const [therapeuticPrompts, setTherapeuticPrompts] = useState(null)
  const [currentPromptIndex, setCurrentPromptIndex] = useState(0)
  const [therapeuticResponses, setTherapeuticResponses] = useState([])
  const [currentResponse, setCurrentResponse] = useState('')
  const [sessionProgress, setSessionProgress] = useState('check-in') // check-in, path-selection, journaling, reflection
  const [emotionalCheckIn, setEmotionalCheckIn] = useState('')
  const [showClosingReflection, setShowClosingReflection] = useState(false)
  const [closingReflection, setClosingReflection] = useState(null)
  const [entryContent, setEntryContent] = useState('')

  // Mood options for emotional check-in
  const moodOptions = [
    { value: 'joyful', label: 'Joyful', emoji: '😊' },
    { value: 'grateful', label: 'Grateful', emoji: '🙏' },
    { value: 'peaceful', label: 'Peaceful', emoji: '😌' },
    { value: 'excited', label: 'Excited', emoji: '🤗' },
    { value: 'neutral', label: 'Neutral', emoji: '😐' },
    { value: 'thoughtful', label: 'Thoughtful', emoji: '🤔' },
    { value: 'tired', label: 'Tired', emoji: '😴' },
    { value: 'stressed', label: 'Stressed', emoji: '😰' },
    { value: 'sad', label: 'Sad', emoji: '😢' },
    { value: 'anxious', label: 'Anxious', emoji: '😟' },
    { value: 'frustrated', label: 'Frustrated', emoji: '😤' },
    { value: 'overwhelmed', label: 'Overwhelmed', emoji: '😵' }
  ]

  // Mock transcription text
  const mockTranscription = "I've been feeling really good today. The meditation session this morning helped me start the day with a clear mind. I'm grateful for the support I've been receiving and I can see real progress in managing my anxiety."

  const handleStartRecording = () => {
    setIsRecording(true)
    setTranscription('')
    setHasRecording(false)

    // In a real app, you would start actual recording here
    console.log('Starting voice recording...')

    // Mock progressive transcription
    let progress = 0
    const transcriptionWords = mockTranscription.split(' ')
    const interval = setInterval(() => {
      progress += 1
      const wordsToShow = transcriptionWords.slice(0, Math.floor((progress / 30) * transcriptionWords.length))
      setTranscription(wordsToShow.join(' '))

      if (progress >= 30) { // 3 seconds
        clearInterval(interval)
        setIsRecording(false)
        setHasRecording(true)
        setTranscription(mockTranscription)
        console.log('Recording completed')
      }
    }, 100)
  }

  const handleStopRecording = () => {
    setIsRecording(false)
    setHasRecording(true)
    setTranscription(mockTranscription)
  }

  const handlePlayRecording = () => {
    setIsPlaying(true)
    // Mock playback
    setTimeout(() => {
      setIsPlaying(false)
    }, 2000)
  }

  const handleSaveEntry = () => {
    console.log('Saving voice journal entry:', transcription)
    // Here you would save to backend
    setHasRecording(false)
    setTranscription('')
  }

  const handleDeleteRecording = () => {
    setHasRecording(false)
    setTranscription('')
    setIsRecording(false)
  }

  const handleAIReply = () => {
    console.log('Requesting AI empathetic reply...')
    // Here you would request AI response
  }

  // ===================================================================
  // 🧠 THERAPEUTIC VOICE JOURNALING FUNCTIONS
  // ===================================================================

  // Start therapeutic voice journaling session
  const handleStartTherapeuticJournal = async () => {
    try {
      setIsLoading(true)
      setError(null)

      console.log('🧠 Starting therapeutic voice journaling session...')

      // Start therapeutic session with emotional check-in
      const response = await journalAPI.startTherapeuticSession('neutral', {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        sessionType: 'voice-journaling'
      })

      if (response.success) {
        setTherapeuticSession({
          ...response.data,
          sessionId: response.data.sessionId || Date.now().toString()
        })
        setShowTherapeuticModal(true)
        setSessionProgress('check-in')
        console.log('✅ Therapeutic session started:', response.data)
      } else {
        throw new Error(response.message || 'Failed to start therapeutic session')
      }
    } catch (error) {
      console.error('❌ Failed to start therapeutic session:', error)
      setError('Failed to start therapeutic session')
      // Fallback to mock data for demo
      setTherapeuticSession({
        sessionId: Date.now().toString(),
        welcomeMessage: "Welcome to your therapeutic voice journaling session. Let's begin with a gentle check-in.",
        adaptivePaths: []
      })
      setShowTherapeuticModal(true)
      setSessionProgress('check-in')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle emotional check-in response
  const handleEmotionalCheckIn = async (emotionalState) => {
    try {
      setEmotionalCheckIn(emotionalState)
      setIsLoading(true)

      // Get adaptive journaling paths based on emotional state
      const response = await journalAPI.getAdaptiveJournalingPaths(emotionalState, [])

      if (response.success) {
        setTherapeuticSession(prev => ({
          ...prev,
          adaptivePaths: response.data,
          emotionalState
        }))
        setSessionProgress('path-selection')
      }
    } catch (error) {
      console.error('Failed to get adaptive paths:', error)
      setError('Failed to get journaling paths')
      // Fallback to mock paths
      setTherapeuticSession(prev => ({
        ...prev,
        adaptivePaths: [
          {
            id: 'emotion_processing',
            title: 'Emotion Processing',
            description: 'Explore and understand your current feelings',
            icon: '💭'
          },
          {
            id: 'cognitive_reframe',
            title: 'Cognitive Reframing',
            description: 'Challenge and reframe unhelpful thoughts',
            icon: '🔄'
          },
          {
            id: 'gratitude_reflection',
            title: 'Gratitude Reflection',
            description: 'Focus on positive aspects and appreciation',
            icon: '🙏'
          }
        ],
        freeWriteOption: "Or simply speak freely about whatever is on your mind today."
      }))
      setSessionProgress('path-selection')
    } finally {
      setIsLoading(false)
    }
  }

  // Handle journaling path selection
  const handlePathSelection = async (pathId) => {
    try {
      setSelectedJournalingPath(pathId)
      setIsLoading(true)

      // Get therapeutic prompts for selected path
      const response = await journalAPI.getTherapeuticPrompts(pathId, emotionalCheckIn)

      if (response.success) {
        setTherapeuticPrompts(response.data)
        setSessionProgress('journaling')
        setCurrentPromptIndex(0)
      }
    } catch (error) {
      console.error('Failed to get therapeutic prompts:', error)
      setError('Failed to get journaling prompts')
      // Fallback to mock prompts
      setTherapeuticPrompts({
        guidanceText: "Let's explore your thoughts and feelings through voice journaling.",
        primaryPrompt: "Take a moment to speak about what's on your mind right now. How are you feeling, and what thoughts are present for you?",
        technique: 'reflection',
        followUpQuestions: [
          "What emotions are you noticing as you reflect on this?",
          "How might you view this situation from a different perspective?"
        ]
      })
      setSessionProgress('journaling')
      setCurrentPromptIndex(0)
    } finally {
      setIsLoading(false)
    }
  }

  // Handle therapeutic voice response submission
  const handleTherapeuticVoiceResponse = async (voiceTranscription) => {
    try {
      setIsLoading(true)

      // Process response with AI guidance
      const processingResponse = await journalAPI.processTherapeuticResponse(
        voiceTranscription,
        therapeuticPrompts?.technique || 'reflection',
        {
          sessionId: therapeuticSession?.sessionId,
          pathType: selectedJournalingPath,
          emotionalState: emotionalCheckIn,
          responseType: 'voice'
        }
      )

      if (processingResponse.success) {
        // Add response to therapeutic responses
        setTherapeuticResponses(prev => [...prev, {
          prompt: therapeuticPrompts?.primaryPrompt,
          response: voiceTranscription,
          aiGuidance: processingResponse.data,
          timestamp: new Date().toISOString(),
          type: 'voice'
        }])

        // Add to entry content
        setEntryContent(prev => prev + (prev ? '\n\n' : '') + voiceTranscription)

        // Check if we should continue with follow-up questions or move to reflection
        if (therapeuticPrompts?.followUpQuestions && currentPromptIndex < therapeuticPrompts.followUpQuestions.length) {
          setCurrentPromptIndex(prev => prev + 1)
        } else {
          // Move to closing reflection
          await handleGenerateClosingReflection()
        }
      }
    } catch (error) {
      console.error('Failed to process therapeutic response:', error)
      setError('Failed to process your voice response')
    } finally {
      setIsLoading(false)
    }
  }

  // Generate closing reflection
  const handleGenerateClosingReflection = async () => {
    try {
      setIsLoading(true)

      const response = await journalAPI.generateClosingReflection(
        therapeuticSession,
        entryContent
      )

      if (response.success) {
        setClosingReflection(response.data)
        setSessionProgress('reflection')
        setShowClosingReflection(true)
      }
    } catch (error) {
      console.error('Failed to generate closing reflection:', error)
      setError('Failed to generate closing reflection')
      // Fallback to mock reflection
      setClosingReflection({
        acknowledgment: "Thank you for sharing your thoughts and feelings through voice journaling today.",
        reflection: "Your willingness to explore your inner world shows great courage and self-awareness.",
        integrationQuestion: "How might you carry these insights with you into the rest of your day?",
        encouragement: "Remember, every step in your journey of self-discovery is valuable and meaningful."
      })
      setSessionProgress('reflection')
      setShowClosingReflection(true)
    } finally {
      setIsLoading(false)
    }
  }

  // Complete therapeutic journaling session
  const handleCompleteTherapeuticSession = async () => {
    try {
      setIsLoading(true)

      // Save the journal entry with therapeutic context
      const entryData = {
        content: entryContent,
        mood: emotionalCheckIn,
        title: `Voice Journal - ${new Date().toLocaleDateString()}`,
        tags: ['therapeutic-voice-journaling', selectedJournalingPath],
        therapeuticData: {
          sessionId: therapeuticSession?.sessionId,
          pathType: selectedJournalingPath,
          responses: therapeuticResponses,
          closingReflection: closingReflection,
          sessionType: 'voice'
        }
      }

      const response = await journalAPI.createEntry(
        entryData.content,
        entryData.mood,
        entryData.title,
        entryData.tags
      )

      if (response.success) {
        console.log('✅ Therapeutic voice journal entry saved:', response.data)
        resetTherapeuticSession()
        setShowTherapeuticModal(false)
      } else {
        throw new Error(response.message || 'Failed to save entry')
      }
    } catch (error) {
      console.error('❌ Failed to complete therapeutic session:', error)
      setError('Failed to save your therapeutic session')
    } finally {
      setIsLoading(false)
    }
  }

  // Reset therapeutic session
  const resetTherapeuticSession = () => {
    setTherapeuticSession(null)
    setSelectedJournalingPath(null)
    setTherapeuticPrompts(null)
    setCurrentPromptIndex(0)
    setTherapeuticResponses([])
    setCurrentResponse('')
    setSessionProgress('check-in')
    setEmotionalCheckIn('')
    setShowClosingReflection(false)
    setClosingReflection(null)
    setEntryContent('')
  }

  const getStatusText = () => {
    if (isRecording) return 'Recording... Speak naturally'
    if (hasRecording) return 'Recording complete. Review your entry below.'
    return 'Tap to start voice journaling'
  }

  return (
    <Card className="widget--voice">
      <div className="widget__header">
        <h3 className="widget__title">
          <Mic className="widget__icon" />
          Voice Journal
        </h3>
        <button
          className="btn btn--therapeutic"
          onClick={handleStartTherapeuticJournal}
          disabled={isLoading}
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            padding: '8px 12px',
            fontSize: '12px',
            fontWeight: '600',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
        >
          <Brain size={14} />
          Therapeutic
        </button>
      </div>

      <div className="voice-widget__recorder">
        <motion.button
          className={`voice-widget__record-button ${isRecording ? 'voice-widget__record-button--recording' : ''
            }`}
          onClick={isRecording ? handleStopRecording : handleStartRecording}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          disabled={hasRecording}
        >
          {isRecording ? <Square size={32} /> : <Mic size={32} />}
        </motion.button>

        <div className="voice-widget__status">
          {getStatusText()}
        </div>
      </div>

      {transcription && (
        <motion.div
          className="voice-widget__transcription"
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          transition={{ duration: 0.3 }}
        >
          <p className="voice-widget__transcription-text">
            "{transcription}"
          </p>
        </motion.div>
      )}

      {hasRecording && (
        <motion.div
          className="voice-widget__actions"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <button
            className="btn btn--outline btn--small"
            onClick={handlePlayRecording}
            disabled={isPlaying}
          >
            {isPlaying ? (
              <Volume2 size={16} />
            ) : (
              <Play size={16} />
            )}
            {isPlaying ? 'Playing...' : 'Play'}
          </button>

          <button
            className="btn btn--secondary btn--small"
            onClick={handleSaveEntry}
          >
            <Save size={16} />
            Save Entry
          </button>

          <button
            className="btn btn--outline btn--small"
            onClick={handleAIReply}
          >
            <Volume2 size={16} />
            AI Reply
          </button>

          <button
            className="btn btn--outline btn--small"
            onClick={handleDeleteRecording}
            style={{ color: '#EF4444', borderColor: '#EF4444' }}
          >
            <Trash2 size={16} />
            Delete
          </button>
        </motion.div>
      )}

      {!hasRecording && !isRecording && (
        <div style={{
          textAlign: 'center',
          color: 'var(--gray-600)',
          fontSize: 'var(--font-size-sm)',
          marginTop: 'var(--spacing-md)'
        }}>
          <p>🎙️ Voice journaling with AI transcription</p>
          <p style={{ fontSize: 'var(--font-size-xs)', marginTop: 'var(--spacing-xs)' }}>
            Powered by Whisper STT & ElevenLabs TTS
          </p>
        </div>
      )}

      {/* Therapeutic Voice Journaling Modal */}
      {showTherapeuticModal && createPortal(
        <AnimatePresence>
          <motion.div
            className="therapeutic-modal-overlay"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 1000,
              padding: '20px'
            }}
          >
            <motion.div
              className="therapeutic-modal"
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              style={{
                background: 'white',
                borderRadius: '16px',
                padding: '32px',
                maxWidth: '600px',
                width: '100%',
                maxHeight: '80vh',
                overflowY: 'auto',
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'
              }}
            >
              <div className="therapeutic-modal__header">
                <h2 style={{
                  fontSize: '24px',
                  fontWeight: '700',
                  color: '#1a202c',
                  marginBottom: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px'
                }}>
                  <Brain size={28} style={{ color: '#667eea' }} />
                  Therapeutic Voice Journaling
                </h2>
                <button
                  onClick={() => {
                    setShowTherapeuticModal(false)
                    resetTherapeuticSession()
                  }}
                  style={{
                    position: 'absolute',
                    top: '16px',
                    right: '16px',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    padding: '8px',
                    borderRadius: '8px',
                    color: '#64748b'
                  }}
                >
                  <X size={20} />
                </button>
              </div>

              <div className="therapeutic-modal__content">
                {isLoading && (
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    padding: '40px',
                    color: '#64748b'
                  }}>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    >
                      <Brain size={32} style={{ color: '#667eea' }} />
                    </motion.div>
                    <p style={{ marginTop: '16px' }}>Preparing your therapeutic session...</p>
                  </div>
                )}

                {error && (
                  <div style={{
                    background: '#fef2f2',
                    border: '1px solid #fecaca',
                    borderRadius: '8px',
                    padding: '16px',
                    marginBottom: '20px',
                    color: '#dc2626'
                  }}>
                    {error}
                  </div>
                )}

                {sessionProgress === 'check-in' && therapeuticSession && !isLoading && (
                  <motion.div
                    className="therapeutic-checkin"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div style={{ marginBottom: '24px' }}>
                      <p style={{
                        fontSize: '16px',
                        color: '#4a5568',
                        lineHeight: '1.6',
                        marginBottom: '20px'
                      }}>
                        {therapeuticSession.welcomeMessage || "Welcome to your therapeutic voice journaling session. Let's begin with a gentle check-in."}
                      </p>
                    </div>

                    <div className="therapeutic-checkin__emotions">
                      <h4 style={{
                        fontSize: '18px',
                        fontWeight: '600',
                        color: '#2d3748',
                        marginBottom: '16px'
                      }}>
                        How are you feeling right now?
                      </h4>
                      <div style={{
                        display: 'grid',
                        gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
                        gap: '12px'
                      }}>
                        {moodOptions.map((mood) => (
                          <button
                            key={mood.value}
                            onClick={() => handleEmotionalCheckIn(mood.value)}
                            style={{
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              padding: '16px 12px',
                              border: emotionalCheckIn === mood.value ? '2px solid #667eea' : '2px solid #e2e8f0',
                              borderRadius: '12px',
                              background: emotionalCheckIn === mood.value ? '#f7fafc' : 'white',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease',
                              fontSize: '14px',
                              fontWeight: '500'
                            }}
                          >
                            <span style={{ fontSize: '24px', marginBottom: '8px' }}>{mood.emoji}</span>
                            <span style={{ color: '#4a5568' }}>{mood.label}</span>
                          </button>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}

                {sessionProgress === 'path-selection' && therapeuticSession?.adaptivePaths && !isLoading && (
                  <motion.div
                    className="therapeutic-paths"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div style={{ marginBottom: '24px' }}>
                      <h3 style={{
                        fontSize: '20px',
                        fontWeight: '600',
                        color: '#2d3748',
                        marginBottom: '8px'
                      }}>
                        Choose how you'd like to use your voice journal today:
                      </h3>
                      <p style={{
                        fontSize: '16px',
                        color: '#4a5568',
                        lineHeight: '1.6'
                      }}>
                        Based on how you're feeling, here are three paths designed for you:
                      </p>
                    </div>

                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '12px',
                      marginBottom: '24px'
                    }}>
                      {therapeuticSession.adaptivePaths.map((path) => (
                        <motion.button
                          key={path.id}
                          onClick={() => handlePathSelection(path.id)}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            padding: '20px',
                            border: '2px solid #e2e8f0',
                            borderRadius: '12px',
                            background: 'white',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            textAlign: 'left'
                          }}
                        >
                          <div style={{
                            fontSize: '32px',
                            marginRight: '16px'
                          }}>
                            {path.icon}
                          </div>
                          <div>
                            <h4 style={{
                              fontSize: '16px',
                              fontWeight: '600',
                              color: '#2d3748',
                              marginBottom: '4px'
                            }}>
                              {path.title}
                            </h4>
                            <p style={{
                              fontSize: '14px',
                              color: '#4a5568',
                              lineHeight: '1.5'
                            }}>
                              {path.description}
                            </p>
                          </div>
                        </motion.button>
                      ))}
                    </div>

                    <div style={{
                      padding: '16px',
                      background: '#f7fafc',
                      borderRadius: '12px',
                      textAlign: 'center'
                    }}>
                      <p style={{
                        fontSize: '14px',
                        color: '#4a5568',
                        marginBottom: '12px'
                      }}>
                        {therapeuticSession.freeWriteOption || "Or simply speak freely about whatever is on your mind today."}
                      </p>
                      <button
                        onClick={() => handlePathSelection('free_write')}
                        style={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          gap: '8px',
                          padding: '8px 16px',
                          background: '#667eea',
                          color: 'white',
                          border: 'none',
                          borderRadius: '8px',
                          fontSize: '14px',
                          fontWeight: '500',
                          cursor: 'pointer',
                          transition: 'all 0.2s ease'
                        }}
                      >
                        <PenTool size={16} />
                        Free Write
                      </button>
                    </div>
                  </motion.div>
                )}

                {sessionProgress === 'journaling' && therapeuticPrompts && !isLoading && (
                  <motion.div
                    className="therapeutic-journaling"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div style={{ marginBottom: '24px' }}>
                      <h3 style={{
                        fontSize: '20px',
                        fontWeight: '600',
                        color: '#2d3748',
                        marginBottom: '12px'
                      }}>
                        {therapeuticPrompts.guidanceText}
                      </h3>
                      <div style={{
                        padding: '20px',
                        background: '#f7fafc',
                        borderRadius: '12px',
                        border: '1px solid #e2e8f0'
                      }}>
                        <h4 style={{
                          fontSize: '16px',
                          fontWeight: '500',
                          color: '#2d3748',
                          lineHeight: '1.6'
                        }}>
                          {currentPromptIndex === 0
                            ? therapeuticPrompts.primaryPrompt
                            : therapeuticPrompts.followUpQuestions?.[currentPromptIndex - 1]
                          }
                        </h4>
                      </div>
                    </div>

                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      gap: '20px'
                    }}>
                      <motion.button
                        className={`voice-widget__record-button ${isRecording ? 'voice-widget__record-button--recording' : ''}`}
                        onClick={isRecording ? () => {
                          handleStopRecording()
                          if (transcription) {
                            handleTherapeuticVoiceResponse(transcription)
                          }
                        } : handleStartRecording}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        style={{
                          width: '80px',
                          height: '80px',
                          borderRadius: '50%',
                          border: 'none',
                          background: isRecording
                            ? 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'
                            : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                          color: 'white',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                          transition: 'all 0.2s ease'
                        }}
                      >
                        {isRecording ? <Square size={32} /> : <Mic size={32} />}
                      </motion.button>

                      <div style={{
                        textAlign: 'center',
                        color: '#4a5568'
                      }}>
                        {isRecording ? 'Recording... Speak naturally' : 'Tap to start recording your response'}
                      </div>

                      {transcription && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          style={{
                            width: '100%',
                            padding: '16px',
                            background: '#f7fafc',
                            borderRadius: '12px',
                            border: '1px solid #e2e8f0'
                          }}
                        >
                          <p style={{
                            fontSize: '14px',
                            color: '#2d3748',
                            lineHeight: '1.6',
                            fontStyle: 'italic'
                          }}>
                            "{transcription}"
                          </p>
                        </motion.div>
                      )}
                    </div>
                  </motion.div>
                )}

                {sessionProgress === 'reflection' && closingReflection && !isLoading && (
                  <motion.div
                    className="therapeutic-reflection"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div style={{ marginBottom: '24px' }}>
                      <h3 style={{
                        fontSize: '20px',
                        fontWeight: '600',
                        color: '#2d3748',
                        marginBottom: '16px'
                      }}>
                        Session Reflection
                      </h3>
                    </div>

                    <div style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '20px'
                    }}>
                      <div style={{
                        padding: '20px',
                        background: '#f0fff4',
                        borderRadius: '12px',
                        border: '1px solid #86efac'
                      }}>
                        <h4 style={{
                          fontSize: '16px',
                          fontWeight: '600',
                          color: '#166534',
                          marginBottom: '8px'
                        }}>
                          Acknowledgment
                        </h4>
                        <p style={{
                          fontSize: '14px',
                          color: '#166534',
                          lineHeight: '1.6'
                        }}>
                          {closingReflection.acknowledgment}
                        </p>
                      </div>

                      <div style={{
                        padding: '20px',
                        background: '#fef3c7',
                        borderRadius: '12px',
                        border: '1px solid #fbbf24'
                      }}>
                        <h4 style={{
                          fontSize: '16px',
                          fontWeight: '600',
                          color: '#92400e',
                          marginBottom: '8px'
                        }}>
                          Reflection
                        </h4>
                        <p style={{
                          fontSize: '14px',
                          color: '#92400e',
                          lineHeight: '1.6'
                        }}>
                          {closingReflection.reflection}
                        </p>
                      </div>

                      <div style={{
                        padding: '20px',
                        background: '#ede9fe',
                        borderRadius: '12px',
                        border: '1px solid #a78bfa'
                      }}>
                        <h4 style={{
                          fontSize: '16px',
                          fontWeight: '600',
                          color: '#5b21b6',
                          marginBottom: '8px'
                        }}>
                          Integration Question
                        </h4>
                        <p style={{
                          fontSize: '14px',
                          color: '#5b21b6',
                          lineHeight: '1.6',
                          marginBottom: '12px'
                        }}>
                          {closingReflection.integrationQuestion}
                        </p>
                        <textarea
                          placeholder="Your final reflection..."
                          style={{
                            width: '100%',
                            padding: '12px',
                            border: '1px solid #d1d5db',
                            borderRadius: '8px',
                            fontSize: '14px',
                            lineHeight: '1.5',
                            resize: 'vertical',
                            minHeight: '80px'
                          }}
                          onChange={(e) => setEntryContent(prev => prev + '\n\nFinal Reflection: ' + e.target.value)}
                        />
                      </div>

                      <div style={{
                        padding: '20px',
                        background: '#fef2f2',
                        borderRadius: '12px',
                        border: '1px solid #fca5a5',
                        textAlign: 'center'
                      }}>
                        <p style={{
                          fontSize: '14px',
                          color: '#dc2626',
                          lineHeight: '1.6',
                          fontWeight: '500'
                        }}>
                          {closingReflection.encouragement}
                        </p>
                      </div>

                      <div style={{
                        display: 'flex',
                        justifyContent: 'center',
                        paddingTop: '20px'
                      }}>
                        <button
                          onClick={handleCompleteTherapeuticSession}
                          disabled={isLoading}
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            padding: '12px 24px',
                            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                            color: 'white',
                            border: 'none',
                            borderRadius: '12px',
                            fontSize: '16px',
                            fontWeight: '600',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease',
                            boxShadow: '0 4px 12px rgba(16, 185, 129, 0.3)'
                          }}
                        >
                          <Save size={16} />
                          {isLoading ? 'Saving...' : 'Complete Session'}
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
            </motion.div>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}
    </Card>
  )
}

export default VoiceJournalWidget
